<template>
  <Row>
    <Col span="12" offset="6">
      <Card>
        <Form ref="courseUpdate" :model="courseUpdate" :rules="courseRule" :label-width="100">
          <form-item prop="name" label="课程名称">
            <Input v-model="courseUpdate.name" type="text" />
          </form-item>
          <form-item prop="code" label="课程编号">
            <Input v-model="courseUpdate.code" type="text" />
          </form-item>
          <form-item prop="underClassExam" label="课下 Exam ID">
            <Input v-model="courseUpdate.underClassExam" type="number" />
          </form-item>
          <form-item prop="opening" label="课程是否开放">
            <radio-group v-model="courseUpdate.opening">
              <radio label="是">是</radio>
              <radio label="否">否</radio>
            </radio-group>
          </form-item>
          <form-item prop="tutorialOpening" label="教程是否开放">
            <radio-group v-model="courseUpdate.tutorialOpening">
              <radio label="是">是</radio>
              <radio label="否">否</radio>
            </radio-group>
          </form-item>
          <form-item prop="tutorialSite" label="教程网站地址">
            <Input v-model="courseUpdate.tutorialSite" type="text" />
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit('courseUpdate')">确认修改</Button>
          </form-item>
          <form-item label="设置助教">
            <Select v-model="selectedAssistants" multiple filterable placeholder="从 allowed users 中选择助教">
              <Option v-for="user in allowedUsers" :value="user.id" :key="user.id">
                {{ user.username }}
              </Option>
            </Select>
          </form-item>

          <form-item>
            <Button type="primary" @click="submitAssistants">提交助教设置</Button>
          </form-item>
        </Form>
      </Card>
    </Col>
    <Col span="20" offset="2">
      <br />
      <CourseAnnounce />
    </Col>
  </Row>
</template>

<script>
import { courseIdReq, getAllowedUserReq, assistantReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'
import CourseAnnounce from './course-announce.vue'

export default {
  name: 'CourseUpdate',
  data() {
    return {
      assistants: [],
      allowedUsers: [],
      selectedAssistants: [],
      currentCourses: [],
      courseUpdate: {
        code: null,
        name: null,
        opening: null,
        tutorialOpening: null,
        tutorialSite: null,
        underClassExam: null
      },
      courseRule: {
        name: [{ required: true, message: '请填写课程名称', trigger: 'blur' }],
        code: [{ required: true, message: '请填写课程编号', trigger: 'blur' }],
        tutorialSite: [{ required: true, message: '请填写课程网址', trigger: 'blur' }],
        // opening: [{ required: true, message: '请选择课程是否开放', trigger: 'blur' }],
        // tutorialOpening: [{ required: true, message: '请选择教程是否开放', trigger: 'blur' }],
        announcements: [{ required: false }],
        underClassExam: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (isNaN(parseInt(value))) {
                callback(new Error('请输入合法的 Exam ID'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  computed: {
    trueCourseUpdate() {
      const result = {
        name: this.courseUpdate.name,
        code: this.courseUpdate.code,
        tutorial_site: this.courseUpdate.tutorialSite,
        opening: this.courseUpdate.opening === '是',
        tutorial_opening: this.courseUpdate.tutorialOpening === '是',
        under_class_exam: null,
        announcements: this.courseUpdate.announcements
      }
      if (this.courseUpdate.underClassExam !== null) {
        result.under_class_exam = parseInt(this.courseUpdate.underClassExam)
      } else {
        delete result.under_class_exam
      }
      return result
    }
  },
  async mounted() {
    await this.loadData()
    this.loadAssistantsAndAllowedUsers()
  },
  methods: {
    getCourseInfo() {
      return courseIdReq('get', this.$route.params.id, {})
    },
    async loadData() {
      try {
        const cur = await this.getCourseInfo()
        this.courseUpdate.code = cur.data.code
        this.courseUpdate.name = cur.data.name
        this.courseUpdate.underClassExam = cur.data.under_class_exam === -1 ? null : cur.data.under_class_exam
        this.courseUpdate.tutorialSite = cur.data.tutorial_site
        this.courseUpdate.opening = cur.data.opening ? '是' : '否'
        this.courseUpdate.tutorialOpening = cur.data.tutorial_opening ? '是' : '否'
        this.courseUpdate.announcements = cur.data.announcements
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    async loadAssistantsAndAllowedUsers() {
      const id = this.$route.params.id
      try {
        const assistantsRes = await assistantReq('get', id, {})
        this.assistants = assistantsRes.data.data
        this.selectedAssistants = assistantsRes.data.data.map((user) => user.id)

        const allowedUsersRes = await getAllowedUserReq(id, {})
        this.allowedUsers = allowedUsersRes.data.data
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    async submitAssistants() {
      const id = this.$route.params.id
      try {
        await assistantReq('post', id, { assistant_ids: this.selectedAssistants })
        this.$Notice.success({ title: '助教设置成功' })
        await this.loadAssistantsAndAllowedUsers()
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    update(data) {
      return courseIdReq('put', this.$route.params.id, data)
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.update(this.trueCourseUpdate)
            .then(() => {
              this.$Notice.success({ title: '更新成功' })
              this.$router.push({ name: 'course_table' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    }
  },
  components: { CourseAnnounce }
}
</script>
