import { loginReq } from '@/api/auth'

export default {
  namespaced: true,
  state: {
    token: '',
    refreshToken: '',
    userName: '',
    access: '',
    userDefaultCourse: -1
  },
  mutations: {
    setToken(state, token) {
      state.token = token
    },
    setRefreshToken(state, token) {
      state.refreshToken = token
    },
    setUserName(state, name) {
      state.userName = name
    },
    setUserDefaultCourse(state, id) {
      state.userDefaultCourse = id
    }
  },
  getters: {
    token: (state) => state.token,
    refreshToken: (state) => state.refreshToken,
    userName: (state) => state.userName
  },
  actions: {
    handleLogin({ commit }, { userName, password }) {
      userName = userName.trim()
      return new Promise((resolve, reject) => {
        loginReq({
          username: userName,
          password
        })
          .then((res) => {
            const data = res.data
            commit('setToken', data['access_token'])
            commit('setRefreshToken', data['refresh_token'])
            commit('setUserName', userName)
            resolve()
          })
          .catch((err) => {
            if (err.response && err.response.data && err.response.data['detailed_error_code'] === 40003) {
              window.location.href = 'https://www.bilibili.com/video/BV1GJ411x7h7'
            } else {
              reject(err)
            }
          })
      })
    },
    handleLogOut() {
      localStorage.clear()
      location.reload()
    }
  }
}
