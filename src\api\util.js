import HttpRequest from '@/libs/axios'

export const direct = (url, method, options) => {
  return new HttpRequest().request({ url, method, ...options })
}

export const getRequest = (url, method, params) => {
  const options = {}
  if (typeof params !== 'undefined') {
    if (method === 'get') {
      options.params = params
    } else {
      options.data = params
    }
  }
  return direct(url, method, options)
}

export const getRequestForFile = (url, method, params) => {
  const options = {}
  if (method === 'get') {
    options.params = params
    options.responseType = 'blob'
  } else {
    const formData = new FormData()
    formData.append('file', params)
    options.data = formData
    options.params = { filename: params.name }
    options.headers = { 'Content-Type': 'multipart/form-data' }
  }
  return direct(url, method, options)
}

export const getRequestForAnnounce = (url, method, params) => {
  const options = {}
  if (typeof params !== 'undefined') {
    if (method === 'get') {
      options.params = { course_id: params.course_id }
    } else if (method === 'put') {
      options.data = { announcement_text: params.announcement_text }
      options.params = { course_id: params.course_id }
    } else if (method === 'delete') {
      options.params = { course_id: params.course_id }
      if (typeof params.all !== 'undefined') {
        if (params.all === true) {
          options.data = { all: true }
        }
      } else {
        options.data = { announcement_id: params.announcement_id }
      }
    } else if (method === 'post') {
      options.data = {
        announcement_id: params.announcement_id,
        announcement_content: params.announcement_content
      }
      options.params = { course_id: params.course_id }
    } else {
      options.data = params
    }
  }
  return direct(url, method, options)
}
