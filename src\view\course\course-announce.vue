<template>
  <div>
    <div>
      <p class="headline">公告管理</p>
      <br /><br />
    </div>
    <Button type="primary" @click="add()">新增 +</Button>
    <br /><br />
    <Table border :columns="columns12" :data="allAnnouncements">
      <template slot-scope="{ row }" slot="id">
        <strong>{{ row.id }}</strong>
      </template>
      <template slot-scope="{ index }" slot="action">
        <Button type="success" size="small" style="margin-right: 5px" @click="show(index)">预览</Button>
        <Button type="info" size="small" style="margin-right: 5px" @click="edit(index)">编辑</Button>
        <Button type="error" size="small" @click="remove(index)">删除</Button>
        <Modal v-model="modal2" title="警告！！" @on-ok="ok2" @on-cancel="cancel_del">
          <p>注意，即将为您删除本条公告</p>
        </Modal>
      </template>
    </Table>
    <br />
    <div style="float: right">
      <Button type="error" @click="modal1 = true">全部删除</Button>
      <Modal v-model="modal1" title="警告！！" @on-ok="ok" @on-cancel="cancel_del">
        <p>注意，即将为您删除本课程全部公告！！！</p>
      </Modal>
    </div>
    <br />
    <template>
      <div v-show="edit_banned === false">
        <Tag :checkable="false">正在编辑 公告 ID：{{ editingId }}</Tag>
      </div>
      <br /><br />
      <mavon-editor :ishljs="true" v-model="editingContent" v-show="!edit_banned" placeholder="公告内容在这里修改..." />
      <br /><br />
      <Button type="default" size="default" @click="cancel()" v-show="!edit_banned">取消</Button>
      <div style="float: right">
        <Button type="info" size="default" @click="submit()" v-show="!edit_banned">提交公告</Button>
      </div>
    </template>
    <modal v-model="isShow" title="课程公告预览" @on-cancel="isShow = false">
      <mavon-render v-model="showContent" :ishljs="true"></mavon-render>
    </modal>
  </div>
</template>

<script>
import { announcementReq } from '@/api/course'
import { getLocalTime } from '@/libs/util'

export default {
  name: 'CourseAnnounce',
  data() {
    return {
      editingContent: '',
      showContent: '',
      isShow: false,
      edit_banned: true,
      editingIndex: -1,
      editingId: -1,
      modal1: false,
      modal2: false,
      delIndex: -1,
      columns12: [
        {
          title: 'ID',
          width: 80,
          slot: 'id'
        },
        {
          title: 'Announcement Content',
          key: 'content'
        },
        {
          title: 'Updated At',
          width: 200,
          key: 'updated_at',
          sortable: true
        },
        {
          title: 'Action',
          slot: 'action',
          width: 200,
          align: 'center'
        }
      ],
      allAnnouncements: []
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await this.getAnnouncement().then((res) => {
        this.allAnnouncements = res.data.data.sort((lhs, rhs) => rhs.id - lhs.id)
        for (let index = 0; index < this.allAnnouncements.length; index++) {
          const timeZ = this.allAnnouncements[index].updated_at
          this.allAnnouncements[index].updated_at = getLocalTime(timeZ)
        }
      })
    },
    show(index) {
      this.cancel()
      this.isShow = true
      this.showContent = this.allAnnouncements[index].content
    },
    remove(index) {
      this.modal2 = true
      this.delIndex = index
      this.edit_banned = true
    },
    removeAll() {
      this.deleteAnnouncement(true, [])
      this.edit_banned = true
      return ''
    },
    ok() {
      this.removeAll()
      location.reload()
      this.$Notice.success({ title: '全部公告删除成功' })
    },
    ok2() {
      this.$Notice.success({ title: '全部公告删除成功' })
      const delId = this.allAnnouncements[this.delIndex].id
      this.deleteAnnouncement(false, [delId])
      this.allAnnouncements.splice(this.delIndex, 1)
    },
    cancel_del() {
      this.$Notice.success({ title: '取消成功' })
    },
    edit(index) {
      this.edit_banned = false
      this.editingIndex = index
      this.editingId = this.allAnnouncements[index].id
      this.editingContent = this.allAnnouncements[index].content
    },
    async submit() {
      await this.editAnnouncement(this.allAnnouncements[this.editingIndex].id, this.editingContent)
      this.edit_banned = true
      this.editingIndex = -1
      this.editingId = -1
      await this.loadData()
    },
    cancel() {
      this.edit_banned = true
      this.editingIndex = -1
      this.editingId = -1
      this.editingContent = '公告内容在这里修改哦...'
    },
    add() {
      this.edit_banned = true
      this.addAnnouncement('new announcement').then((res) => {
        this.editingId = res.data.data[0].id
        this.loadData().then(() => {
          let index = -1
          for (let i = 0; i < this.allAnnouncements.length; i++) {
            if (this.allAnnouncements[i].id === this.editingId) {
              index = i
            }
          }
          this.edit(index)
        })
      })
    },
    getAnnouncement() {
      return announcementReq('get', {
        course_id: parseInt(this.$route.params.id)
      })
    },
    deleteAnnouncement(all, delIds) {
      if (all === true) {
        return announcementReq('delete', {
          all: true,
          course_id: parseInt(this.$route.params.id)
        })
      } else {
        return announcementReq('delete', {
          course_id: parseInt(this.$route.params.id),
          announcement_id: delIds
        })
      }
    },
    addAnnouncement(newContent) {
      return announcementReq('put', {
        course_id: parseInt(this.$route.params.id),
        announcement_text: newContent
      })
    },
    editAnnouncement(announcement_id, newContent) {
      return announcementReq('post', {
        course_id: parseInt(this.$route.params.id),
        announcement_id: announcement_id,
        announcement_content: newContent
      })
    }
  }
}
</script>

<style lang="less" scoped>
.headline {
  font-size: 30px;
}
</style>
