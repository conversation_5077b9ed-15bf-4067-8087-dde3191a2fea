<template>
  <div>
    <Input v-model="roomName" placeholder="请输入教室名称" />
    <Input v-model="roomComment" placeholder="请输入教室备注(可选)" />
    <Button type="success" @click="upload">上传</Button>
    <Tabs>
      <TabPane label="绘制">
        <Row>
          <Col span="12">
            <Input v-model="rowInput" placeholder="请输入画布行数">
              <span slot="prepend">Row</span>
            </Input>
          </Col>
          <Col span="12">
            <Input v-model="colInput" placeholder="请输入画布列数">
              <span slot="prepend">Col</span>
            </Input>
          </Col>
        </Row>
        <Button type="primary" @click="onShapeChange">更改行列设置</Button>
        <Button type="error" @click="reload">清空</Button>
        <Row>
          <Col span="2">
            <br />
          </Col>
          <Col span="20">
            <ordered-select-launchpad v-if="!refreshing" :row="row" :col="col" @on-select="onSelect" />
          </Col>
        </Row>
      </TabPane>
      <TabPane label="导入文件">
        <div style="display: flex; align-items: center">
          <Upload :before-upload="beforeImport" action="">
            <Button icon="ios-cloud-upload-outline">加载 CSV 文件</Button>
          </Upload>
          <label style="margin-left: 10px; font-size: small">文件列名：['name', 'pos_x', 'pos_y', 'comment']</label>
        </div>
        <strong>
          <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
        </strong>
        <Launchpad :value="importedSeats" />
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import { roomReq } from '@/api/room'
import OrderedSelectLaunchpad from '@/view/templates/ordered-select-launchpad'
import { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'
import Launchpad from '@/components/launchpad'
import _ from 'lodash'

export default {
  name: 'RoomUpload',
  components: { Launchpad, OrderedSelectLaunchpad },
  data() {
    return {
      csvColumns: ['name', 'pos_x', 'pos_y', 'comment'],
      importedSeats: { row: 0, col: 0, data: [] },
      roomName: '',
      roomComment: '',
      colInput: '5',
      rowInput: '5',
      col: 5,
      row: 5,
      seats: [],
      refreshing: false
    }
  },
  methods: {
    onSelect(data) {
      // positive pos_x and pos_y required by backend
      this.seats = data.map((item, ind) => ({
        pos_x: item.i + 1,
        pos_y: item.j + 1,
        name: (ind + 1).toString()
      }))
    },
    onShapeChange() {
      const row = Number(this.rowInput)
      const col = Number(this.colInput)
      if (Number.isNaN(row)) {
        this.$Notice.warning({ title: '行数为 NaN' })
      } else if (Number.isNaN(col)) {
        this.$Notice.warning({ title: '列数为 NaN' })
      } else {
        if (this.row === row && this.col === col) return
        if (this.seats.length === 0) {
          this.row = row
          this.col = col
          return
        }
        const vm = this
        this.$Modal.confirm({
          title: 'confirm',
          content: `change shape: (${this.row}, ${this.col}) -> (${row}, ${col})? <br>注意：应用更改后所有已选内容将被清空`,
          onOk() {
            vm.row = row
            vm.col = col
          }
        })
      }
    },
    async reload() {
      this.refreshing = true
      this.roomName = ''
      this.roomComment = ''
      this.seats = []
      await this.$nextTick()
      this.refreshing = false
    },
    upload() {
      const name = this.roomName
      if (name === '') {
        this.$Notice.warning({ title: '课程名称不能为空' })
        return
      }
      const seats = this.seats
      if (seats.length === 0) {
        this.$Notice.warning({ title: '不能上传空教室' })
        return
      }
      const comment = this.roomComment
      const available = true
      roomReq('post', { name, comment, seats, available })
        .then(() => {
          this.$Notice.success({ title: '教室上传成功' })
          this.reload()
          this.$router.push({ name: 'room_table' })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    prepareImportedSeats(data) {
      data = _.map(data, (item) => {
        item.pos_x = Number(item.pos_x)
        item.pos_y = Number(item.pos_y)
        return item
      })
      this.seats = data
      const row = _.max(_.map(data, (item) => Number(item.pos_x)))
      const col = _.max(_.map(data, (item) => Number(item.pos_y)))
      const len = row * col
      const all = _.times(len, () => ({ text: '', type: 'default' }))
      _.forEach(data, ({ pos_x, pos_y, name }) => {
        all[(pos_x - 1) * col + pos_y - 1] = { text: name, type: 'primary' }
      })
      return { data: all, row, col }
    },
    beforeImport(file) {
      getArrayFromFile(file)
        .then((data) => {
          const { tableData } = getTableDataFromArray(data)
          const allowedColumns = this.csvColumns
          const importedData = tableData.map((item) => _.pick(item, allowedColumns))
          this.importedSeats = this.prepareImportedSeats(importedData)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
      return false
    }
  }
}
</script>
