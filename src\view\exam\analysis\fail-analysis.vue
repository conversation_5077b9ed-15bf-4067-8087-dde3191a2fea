<template>
  <Card>
    <Card>
      <Row type="flex" justify="center">
        <Select v-model="selectExam" placeholder="请选择周次" style="width: 200px; margin-bottom: 18px">
          <Option v-for="exam in examList" :key="exam.id" :value="exam.id">{{ exam.name }}</Option>
        </Select>
        <Select v-model="selectProject" placeholder="请选择项目" style="width: 200px; margin-bottom: 18px">
          <Option v-for="project in projectList" :key="project.id" :value="project.id"
            >{{ project.id }} : {{ project.name }}</Option
          >
        </Select>
      </Row>
    </Card>
    <br />
    <Card v-if="conclusion">
      <Row type="flex" justify="center">
        <Col span="24">
          <fail-conclusion :conclusion="conclusion" />
        </Col>
      </Row>
    </Card>
    <Card>
      <Row type="flex" justify="center">
        <Col span="24">
          <fail-chart :chart-data="chartData" height="600px" />
        </Col>
      </Row>
    </Card>
    <Card v-if="tableData">
      <Row type="flex" justify="center">
        <Col span="24">
          <fail-table :table-data="tableData" />
        </Col>
      </Row>
    </Card>
  </Card>
</template>

<script>
import { userProfileReq } from '@/api/user'
import { examReq, examProjectListReq, getProjectFailAnalysis } from '@/api/exam'
import { courseIdReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'
import FailChart from './fail-chart.vue'
import FailTable from './fail-table.vue'
import FailConclusion from './fail-conclusion.vue'

export default {
  name: 'FailAnalysis',
  components: {
    FailChart,
    FailTable,
    FailConclusion
  },
  data() {
    return {
      curCourse: null,
      examList: [],
      projectList: [],
      selectExam: null,
      selectProject: null,
      analysis: null,
      chartData: [],
      tableData: null,
      conclusion: null
    }
  },
  mounted() {
    this.chartData = this.chartDataExample
    this.loadCurCourse()
  },
  watch: {
    selectExam(newVal) {
      if (newVal) {
        this.selectProject = null
        this.loadProjectList()
      } else {
        this.projectList = []
      }
    },
    selectProject(newVal) {
      if (newVal) {
        this.getFailAnalysis()
      }
    }
  },
  methods: {
    loadCurCourse() {
      var underClassExamId
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在用户详情页选择当前课程'
            })
          } else {
            this.curCourse = res.data.course.id
            return courseIdReq('get', this.curCourse, {})
          }
        })
        .then((res) => {
          underClassExamId = res.data.under_class_exam
          return examReq('get', { page_size: 1000, course_id: this.curCourse })
        })
        .then((res) => {
          this.examList = res.data.exams
          for (var i = 0; i < this.examList.length; i++) {
            if (this.examList[i].id === underClassExamId) {
              this.examList[i].name = '课下考试'
            } else {
              this.examList[i].name = this.examList[i].id + ': ' + this.examList[i].date
            }
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    loadProjectList() {
      if (this.selectExam) {
        examProjectListReq('get', this.selectExam)
          .then((res) => {
            this.projectList = res.data.projects
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
      }
    },
    getFailAnalysis() {
      if (this.selectExam && this.selectProject) {
        getProjectFailAnalysis(this.selectProject)
          .then((res) => {
            try {
              this.analysis = res.data.analysis
              this.chartData = this.analysis.chart
              this.tableData = this.analysis.table
              this.conclusion = this.analysis.conclusion
            } catch (error) {
              this.$Modal.error({
                title: '数据解析错误',
                content: error.message
              })
              this.chartData = []
            }
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
            this.chartData = []
          })
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 600px;
}
</style>
