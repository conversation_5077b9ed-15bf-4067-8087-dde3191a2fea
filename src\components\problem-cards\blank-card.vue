<template>
  <Card class="problem-cards">
    <p slot="title">Blank 列表</p>
    <div v-for="(value, i) in answerObject.length" :key="value" class="flex-line">
      <span class="horizontal-margin" style="white-space: nowrap"> 填空 {{ i + 1 }} </span>
      <Input v-model="answerObject[i]" class="horizontal-margin" />
    </div>
  </Card>
</template>

<script>
import './cards.less'

export default {
  name: 'BlankCard',
  props: {
    answer: String
  },
  data() {
    return {
      answerObject: []
    }
  },
  mounted() {
    this.answerObject = JSON.parse(this.answer || '[]')
  }
}
</script>
