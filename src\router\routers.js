import Main from '@/view/index/main'
import { userRouter } from '@/view/user/router'
import { studentRouter } from '@/view/student/router'
import { classRouter } from '@/view/class/router'
import { roomRouter } from '@/view/room/router'
import { courseRouter } from '@/view/course/router'
import { messageRouter } from '@/view/message/router'
import { examRouter } from '@/view/exam/router'
import { onExamRouter } from '@/view/on-exam/router'
import { examCheckRouter } from '@/view/exam-check/router'
import { judgeRouter } from '@/view/judge/router'
import { archiveRouter } from '@/view/archive/router'
import { discussionRouter } from '@/view/discussion/router'

export const title = process.env.VUE_APP_TAG ? 'OA (' + process.env.VUE_APP_TAG + ')' : 'OA System'

export const home = {
  text: '主页',
  name: 'home',
  path: '/judge/judge-table'
}

export default [
  {
    path: '/',
    name: home.name,
    redirect: home.path,
    component: Main,
    meta: {
      notCache: true,
      hideInMenu: true
    }
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: 'Login - 登录',
      hideInMenu: true
    },
    component: () => import('@/view/index/login.vue')
  },
  userRouter,
  studentRouter,
  classRouter,
  roomRouter,
  courseRouter,
  messageRouter,
  examRouter,
  onExamRouter,
  examCheckRouter,
  judgeRouter,
  archiveRouter,
  discussionRouter,
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error/500.vue')
  },
  {
    path: '*',
    name: 'error_404',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error/404.vue')
  }
]
