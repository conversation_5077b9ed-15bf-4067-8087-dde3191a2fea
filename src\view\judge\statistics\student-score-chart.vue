<template>
  <div>
    <div ref="dom" class="charts chart-plot" />
    <div v-if="!value || value.length === 0" style="text-align: center; padding: 20px">暂无数据</div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { on, off } from '@/libs/tools'
import tdTheme from '@/assets/chart.json'

echarts.registerTheme('tdTheme', tdTheme)

export default {
  name: 'StudentScoreChart',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    text: {
      type: String,
      default: ''
    },
    subtext: {
      type: String,
      default: ''
    },
    animation: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dom: null
    }
  },
  watch: {
    value: {
      handler() {
        this.$nextTick(() => {
          this.refresh()
        })
      },
      deep: true
    }
  },
  mounted() {
    this.dom = echarts.init(this.$refs.dom, 'tdTheme')
    if (this.value && this.value.length > 0) {
      this.refresh()
    }
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
    if (this.dom) {
      this.dom.dispose()
    }
  },
  methods: {
    resize() {
      if (this.dom) {
        this.dom.resize()
      }
    },
    refresh() {
      if (!this.dom) return

      this.dom.clear()

      if (!this.value || this.value.length === 0) {
        return
      }

      const option = this.chartOption()
      this.dom.setOption(option)
      on(window, 'resize', this.resize)
    },
    chartOption() {
      // 获取并排序时间戳
      const timestamps = [...(this.value[0]?.timestamps || [])].sort((a, b) => a.localeCompare(b))

      // 根据排序后的时间戳重新排列数据
      const series = this.value.map((item) => ({
        name: item.name,
        type: 'line',
        data: Array.isArray(item.timestamps)
          ? timestamps.map((time) => {
              const index = item.timestamps.indexOf(time)
              return index !== -1 ? parseFloat(item.data[index]) || 0 : 0
            })
          : [],
        smooth: true,
        symbolSize: 8,
        lineStyle: {
          width: 2
        }
      }))

      return {
        animation: this.animation,
        title: {
          text: this.text,
          subtext: this.subtext,
          left: 'center',
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = `${params[0].axisValue}<br/>`
            params.forEach((param) => {
              result += `${param.seriesName}: ${param.value}分<br/>`
            })
            return result
          }
        },
        legend: {
          data: this.value.map((item) => item.name),
          bottom: 10,
          type: 'scroll'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timestamps,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '分数',
          min: 0,
          max: 100,
          splitNumber: 10,
          splitLine: {
            show: true
          }
        },
        series: series
      }
    }
  }
}
</script>

<style scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>
