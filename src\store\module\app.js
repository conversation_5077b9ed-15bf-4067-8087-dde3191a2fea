import {
  getMenuByRouter,
  getBreadCrumbList,
  getHomeRoute,
  getRouteTitleHandled,
  routeHasExist,
  setTagNavListInLocalstorage
} from '@/libs/util'
import routers from '@/router/routers'
import { home } from '@/router/routers'

export default {
  state: {
    tablePage: {},
    tableFilter: {},
    breadCrumbList: [],
    homeRoute: {},
    tagNavList: []
  },
  getters: {
    menuList: (state, getters, rootState) => getMenuByRouter(routers, rootState.user.access)
  },
  mutations: {
    setTablePage(state, payload) {
      state.tablePage[payload.name] = payload.page
    },
    setTableFilter(state, payload) {
      state.tableFilter[payload.name] = payload.filter
    },
    setBreadCrumb(state, route) {
      state.breadCrumbList = getBreadCrumbList(route, state.homeRoute)
    },
    setHomeRoute(state, routes) {
      state.homeRoute = getHomeRoute(routes, home.name)
    },
    updateTagNavList(state, { route, type = 'unshift' }) {
      let router = getRouteTitleHandled(route)
      if (!routeHasExist(state.tagNavList, router)) {
        if (type === 'push') {
          state.tagNavList.push(router)
        } else if (router.name === home.name) {
          state.tagNavList.unshift(router)
        } else {
          state.tagNavList.splice(1, 0, router)
        }
        setTagNavListInLocalstorage([...state.tagNavList])
      }
    }
  }
}
