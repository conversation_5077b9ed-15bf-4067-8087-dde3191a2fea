<template>
  <Row>
    <Col v-for="(aCourse, index) in courses" :key="aCourse.id" span="8">
      <CourseCard :id="aCourse.id" :name="aCourse.name" :code="aCourse.code" @delete="onDelete(index)" />
    </Col>
    <Col span="8">
      <Card style="width: 350px; margin: 20px; text-align: center" @click.native="createCourse">
        <Icon type="ios-add" size="135" />
        <p style="margin-bottom: 20px">创建新课程</p>
      </Card>
    </Col>
  </Row>
</template>

<script>
import CourseCard from './course-card'
import { courseReq } from '@/api/course'
import { userProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
export default {
  name: 'CourseTable',
  components: {
    CourseCard
  },
  data() {
    return {
      courses: []
    }
  },
  mounted() {
    this.loadData()
    if (this.$store.state.user.userDefaultCourse === -1) {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
          } else {
            this.$Modal.info({
              title: '请在用户详情页选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  },
  methods: {
    getCourseList() {
      return courseReq('get', {})
    },
    loadData() {
      this.getCourseList()
        .then((res) => {
          this.courses = res.data.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onDelete(number) {
      this.courses = this.courses.filter((element, index) => {
        return index !== number
      })
    },
    createCourse() {
      this.$router.push({ name: 'course_create' })
    }
  }
}
</script>
