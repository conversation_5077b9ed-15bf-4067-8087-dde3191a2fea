<template>
  <Row>
    <Col span="14" offset="5">
      <Card>
        <Row>
          <Col span="22" offset="1">
            <Form
              ref="addRetake"
              :rules="{ student_id: [{ pattern: /^[0-9]{8}$/, message: '学号格式错误', trigger: 'blur' }] }"
              :model="studentRetake"
              inline
            >
              <form-item prop="student_id">
                <Input
                  v-model="studentRetake.student_id"
                  search
                  enter-button="添加"
                  placeholder="请输入学号"
                  style="width: 200px"
                  @on-search="append(studentRetake.student_id)"
                />
              </form-item>
              <Form-item>
                <div style="display: flex; align-items: center">
                  <Upload :before-upload="beforeUpload" action="">
                    <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
                  </Upload>
                  <label style="margin-left: 10px">CSV 文件列名: ["学号"]</label>
                </div>
                <strong>
                  <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
                </strong>
              </Form-item>
            </Form>
          </Col>
        </Row>
        <Row>
          <Col span="22" offset="1">
            <Table ref="addRetake" :stripe="true" height="500" :data="studentRetake.student" :columns="columns">
              <template slot="userName" slot-scope="{ row }">
                <strong>{{ row.userName }}</strong>
              </template>
              <template slot="action" slot-scope="{ index }">
                <Button type="error" @click="remove(index)">删除</Button>
              </template>
            </Table>
          </Col>
        </Row>
        <br />
        <Row>
          <Col span="2" offset="21">
            <Form ref="addRetake" :model="studentRetake">
              <Form-item>
                <Button
                  long
                  :disabled="studentRetake.student.length === 0"
                  type="primary"
                  @click="handleSubmit('addRetake')"
                >
                  提交
                </Button>
              </Form-item>
            </Form>
          </Col>
        </Row>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { addRetakeStudent } from '@/api/course'
import { getErrModalOptions, getArrayFromFile, getTableDataFromArray } from '@/libs/util'
import { userProfileReq } from '@/api/user'

export default {
  name: 'CourseRetake',
  data() {
    return {
      studentRetake: {
        student_id: '',
        student: []
      },
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 150,
          align: 'center'
        },
        {
          title: '重修用户学号',
          slot: 'userName'
        },
        {
          title: '操作',
          slot: 'action',
          width: 150,
          align: 'center'
        }
      ],
      expectedColumnNames: ['学号'],
      columnNames: []
    }
  },
  computed: {
    uploadFileReady() {
      if (!this.columnNames) {
        return false
      }
      const result = []
      for (let i = 0; i < this.expectedColumnNames.length; i++) {
        const temp = this.expectedColumnNames[i]
        for (let j = 0; j < this.columnNames.length; j++) {
          if (temp === this.columnNames[j]) {
            result.push(temp)
            break
          }
        }
      }
      return this.expectedColumnNames.every((item, index) => item === result[index])
    },
    trueStudentUpdate() {
      return {
        student: this.studentRetake.student.map((data) => data.userName)
      }
    }
  },
  mounted() {
    userProfileReq('get')
      .then((res) => {
        if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
          this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
        } else {
          this.$Modal.info({
            title: '请在课程信息/课程总览选择当前课程'
          })
        }
      })
      .catch((error) => {
        this.$Modal.error(getErrModalOptions(error))
      })
  },
  methods: {
    async beforeUpload(file) {
      try {
        const data = await getArrayFromFile(file)
        const { columns, tableData } = getTableDataFromArray(data)
        this.columnNames = columns.map((item) => item.title)
        if (this.uploadFileReady) {
          this.studentRetake.student = this.studentRetake.student.concat(
            tableData.map((data) => ({ userName: data['学号'] }))
          )
        }
      } catch (err) {
        this.$Notice.warning({ title: '文件格式错误' })
      }
    },
    handleSubmit() {
      addRetakeStudent(this.$store.state.user.userDefaultCourse, this.trueStudentUpdate)
        .then(() => {
          this.$Notice.success({ title: `添加成功` })
          this.$router.push({ name: 'course_table' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    append(name) {
      const pattern = /^[0-9]{8}$/
      if (!pattern.test(name)) {
        this.$Notice.warning({ title: '学号应为 8 位数字' })
        return
      }
      this.studentRetake.student = this.studentRetake.student.concat([{ userName: name }])
    },
    remove(index) {
      this.studentRetake.student.splice(index, 1)
    }
  }
}
</script>
