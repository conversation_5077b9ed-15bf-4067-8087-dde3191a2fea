<template>
  <div>
    <Button type="primary" @click="clearFilter" style="margin-bottom: 17px">清空查询条件</Button>
    <Table :data="filters" :columns="tableColumnsFilters" border stripe />
    <Table ref="table" :show-header="false" :data="data" :columns="columnsWithHandle" border stripe />
  </div>
</template>

<script>
import buttons from '@/view/filter-table/buttons'
import TablesEdit from '@/view/filter-table/edit'
import _ from 'lodash'

export default {
  name: 'FilterTable',
  props: {
    columns: {
      // 列描述数据对象
      type: Array,
      required: true
    },
    data: {
      // 表格数据
      type: Array,
      required: true
    },
    handles: {
      type: Array,
      default: () => []
    },
    defaultFilter: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      filters: [
        {
          title: ''
        }
      ],
      tableColumnsFilters: [],
      search: {}, // 过滤条件保存的对象,就是保存Input框和Select中值
      editingCellId: '',
      editingText: ''
    }
  },
  computed: {
    columnsWithHandle() {
      if (this.handles.length === 0) {
        return this.columns
      }
      if (this.columns.every((item) => item.key !== 'handle')) {
        return [...this.columns, this.supportHandle({ title: 'handle' })]
      }
      return this.columns.map((item) => {
        if (item.key === 'handle') {
          item = this.supportHandle(item)
        }
        if (item.editable) {
          item = this.supportEdit(item)
        }
        return item
      })
    }
  },
  created() {
    this.search = this.defaultFilter
    this.loadFilterHeader()
  },
  mounted() {
    this.search = this.defaultFilter
  },
  methods: {
    exportCsv(params) {
      this.$refs.table.exportCsv(params)
    },
    createOptionsRender(index, h) {
      return _.map(this.columns[index].filter.option || [], (item) => {
        return h(
          'Option',
          {
            props: {
              // Option只能接受字符串或数值类型的值，因此需要把布尔值转化为字符串
              value: typeof item.value === 'boolean' ? String(item.value) : item.value
            }
          },
          item.name
        )
      })
    },
    load() {
      this.$emit('on-search', this.search)
    },
    validInputValue(index, inputValue) {
      if (!inputValue) {
        this.$delete(this.search, this.columns[index].key)
        return
      }
      this.$set(this.search, this.columns[index].key, inputValue)
    },
    supportHandle(item) {
      const handleButtons = this.handles
        .map((item) => {
          if (typeof item === 'string') {
            item = { type: null, text: null, handle: item }
          }
          const handle = item.handle
          const type = item.type || null
          const text = item.text || null
          return buttons[handle](text, type)
        })
        .filter((item) => item)
      item.render = (h, params) => {
        return h(
          'div',
          handleButtons.map((item) => item(h, params, this))
        )
      }
      return item
    },
    supportEdit(item) {
      item.render = (h, params) => {
        const value = this.data[params.index][params.column.key]
        return h(TablesEdit, {
          props: {
            params: params,
            value,
            editingCellId: this.editingCellId,
            editable: true
          },
          on: {
            input: (val) => {
              this.editingText = val
              this.$emit('input', val)
            },
            'on-start-edit': (params) => {
              this.editingCellId = `editing-${params.index}-${params.column.key}`
              this.editingText = this.data[params.index][params.column.key]
              this.$emit('on-start-edit', params)
            },
            'on-cancel-edit': (params) => {
              this.editingCellId = ''
              this.$emit('on-cancel-edit', params)
            },
            'on-save-edit': (params) => {
              const table = this
              if (this.data[params.index][params.column.key] === this.editingText) {
                this.editingCellId = ''
                return
              }
              table.$Modal.confirm({
                title: `确认修改`,
                content: `old: ${this.data[params.index][params.column.key]}<br>new: ${this.editingText}`,
                onOk() {
                  const newVal = {}
                  newVal[params.column.key] = table.editingText
                  table.$emit('on-save-edit', { index: params.index, newVal })
                  table.editingCellId = ''
                },
                onCancel() {
                  table.editingCellId = ''
                  table.$emit('on-cancel-edit')
                }
              })
            }
          }
        })
      }
      return item
    },
    loadFilterHeader() {
      this.editingCellId = ''
      this.editingText = ''
      this.tableColumnsFilters = this.columnsWithHandle.map((item, index) => {
        const filter = {}
        /**
         * 因为是采用的两个表的形式,过滤表中显示查询的Input,Select条件输组件,表头显示的是数据表的表头,渲染的数据是传入的columns中的filter字段
         * 数据表隐藏表头,只显示数据,渲染的数据是传入的columns中的key值
         */
        // 将传入的列描述数据对象(columns) 的title和width 复制给 过滤表的列描述数据对象(tableColumnsFilters)
        this.$set(filter, 'title', item.title)
        if (item.width) {
          this.$set(filter, 'width', item.width)
        }
        if (item.minWidth) {
          this.$set(filter, 'minWidth', item.minWidth)
        } // 支持 minWidth
        let render = () => {}
        if (item.filter) {
          const filterType = item.filter.type || 'Input'
          // 过滤为 下拉选择框
          if (filterType === 'Select' || filterType === 'MultiSelect') {
            render = (h) => {
              const multiple = filterType === 'MultiSelect'
              return h(
                'Select',
                {
                  props: {
                    clearable: true,
                    transfer: true,
                    value: this.search[`${item.key}`],
                    multiple
                  },
                  on: {
                    'on-change': (val) => {
                      if (val === undefined) {
                        this.$delete(this.search, item.key)
                        this.load()
                        return
                      }
                      this.$set(this.search, item.key, val)
                      this.load()
                    }
                  }
                },
                this.createOptionsRender(index, h)
              )
            }
          } else {
            render = (h) => {
              let inputValue = ''
              return h('Input', {
                props: {
                  placeholder: '输入' + item.title,
                  icon: 'ios-search',
                  value: this.search[`${item.key}`]
                },
                on: {
                  input: (val) => {
                    inputValue = val.trim() // fix: 忽略输入首尾的空白字符，注意！！
                    this.validInputValue(index, inputValue)
                  },
                  'on-click': () => {
                    this.load()
                  },
                  'on-enter': () => {
                    this.load()
                  }
                }
              })
            }
          }
        }
        this.$set(filter, 'render', render)
        return filter
      })
    },
    clearFilter() {
      this.search = {}
      this.load()
    }
  }
}
</script>
