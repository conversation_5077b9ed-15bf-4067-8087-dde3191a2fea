<template>
  <div class="custom-bread-crumb">
    <Breadcrumb :style="{ fontSize: `${fontSize}px` }">
      <BreadcrumbItem v-for="item in list" :key="`bread-crumb-${item.name}`" :to="item.to">
        <common-icon :type="item.icon || ''" style="margin-right: 4px; margin-bottom: 3px" />
        {{ showTitle(item) }}
      </BreadcrumbItem>
    </Breadcrumb>
  </div>
</template>

<script>
import './custom-bread-crumb.less'
import { showTitle } from '@/libs/util'
import CommonIcon from '@/components/common-icon'

export default {
  name: 'CustomBreadCrumb',
  components: {
    CommonIcon
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    fontSize: {
      type: Number,
      default: 14
    },
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    showTitle(item) {
      return showTitle(item)
    },
    getCustomIconName(iconName) {
      return iconName.slice(1)
    }
  }
}
</script>
