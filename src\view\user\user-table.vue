<template>
  <Card>
    <filter-table
      :data="userList"
      :columns="columns"
      :default-filter="this.$store.state.app.tableFilter.userTable ? this.$store.state.app.tableFilter.userTable : {}"
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: left">
        <Button type="primary" @click="onCreate">创建新用户</Button>
      </div>
      <div style="float: right">
        <Page :total="totalCnt" :current="curPage" :page-size="pageSize" show-elevator @on-change="changePage" />
      </div>
    </div>
  </Card>
</template>

<script>
import { userListReq, deleteUser } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
import FilterTable from '@/view/filter-table/filter-table'
import _ from 'lodash'
import { ActionButton, LinkButton, Spacer } from '@/libs/render-item'

export default {
  name: 'UserTable',
  components: { FilterTable },
  data() {
    return {
      userList: [],
      columns: [
        {
          title: 'ID',
          key: 'id',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'User Name',
          key: 'username',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Email',
          key: 'email'
        },
        {
          title: 'Action',
          render: (h, params) =>
            h('div', [
              LinkButton(h, params.row.id, 'user_detail', '修改权限', false),
              Spacer(h),
              ActionButton(h, () => this.onDelete(params.row.username), '删除', false)
            ])
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      filter: {}
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.userTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.userTable)
    }
    this.curPage = this.$store.state.app.tablePage.userTable ? this.$store.state.app.tablePage.userTable : 1
    this.loadData(this.curPage)
  },
  methods: {
    loadData(index) {
      userListReq({
        page: index,
        page_size: 10,
        ...this.filter
      })
        .then((res) => {
          this.userList = res.data['users']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      userListReq({
        page: index,
        page_size: 10,
        ...this.filter
      })
        .then((res) => {
          this.userList = res.data['users']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'userTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onDelete(username) {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          deleteUser({
            username: username
          })
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.loadData(1)
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      userListReq({
        page: 1,
        page_size: 10,
        ...this.filter
      })
        .then((res) => {
          this.userList = res.data['users']
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'userTable' })
          this.$store.commit('setTableFilter', { filter: search, name: 'userTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {}
      Object.keys(search).forEach((key) => {
        if (key === 'id') {
          this.filter[key + '__exact'] = search[key]
        } else {
          this.filter[key + '__contains'] = search[key]
        }
      })
      return searchNew
    },
    onCreate() {
      this.$router.push({ name: 'create_user' })
    }
  }
}
</script>
