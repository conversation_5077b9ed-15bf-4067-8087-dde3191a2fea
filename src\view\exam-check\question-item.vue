<template>
  <div>
    <Form>
      <div v-for="([key, value], index) in Object.entries(questionDict)" :key="key" class="question-item">
        <form-item :label="key" :label-width="240" style="margin-bottom: 1px">
          <template v-if="value == 1">
            <Input v-model="innerRecord[index]" @on-change="changeRecord(key, innerRecord[index])" :rows="4" />
          </template>
          <template v-else>
            <div class="radio-group-container">
              <radio-group v-model="innerRecord[index]" @on-change="changeRecord(key, $event)">
                <radio label="A" />
                <radio label="B" />
                <radio label="C" />
                <radio label="F" />
                <radio label="无" />
              </radio-group>
            </div>
          </template>
        </form-item>
      </div>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'QuestionItem',
  props: {
    questionDict: {
      type: Object,
      default: () => ({})
    },
    record: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      innerRecord: []
    }
  },
  beforeMount() {
    Object.keys(this.questionDict).forEach((key) => {
      let tmp = this.record.find((item) => {
        return item.q === key
      })
      this.innerRecord.push(tmp ? tmp.a : '无')
    })
  },
  methods: {
    changeRecord(key, value) {
      let question = key
      let aRecordIndex = this.record.findIndex((item) => {
        return item.q === question
      })
      const next = this.record
      if (aRecordIndex === -1) {
        let newRecord = { q: question, a: '' }
        newRecord.a = value
        next.push(newRecord)
        return undefined
      }
      let aRecord = this.record[aRecordIndex]
      if (value === '无') {
        next.splice(aRecordIndex, 1)
        return undefined
      }
      aRecord.a = value
    }
  }
}
</script>

<style scoped>
.question-item {
  line-height: 3;
  margin-bottom: 10px;
}

.radio-group-container {
  display: flex;
  width: 100%;
}

.radio-group-container >>> .ivu-radio-group {
  display: flex;
  width: 100%;
}

.radio-group-container >>> .ivu-radio-wrapper {
  flex: 1;
  text-align: center;
  margin-right: 0;
}
</style>
