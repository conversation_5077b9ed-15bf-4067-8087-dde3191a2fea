<template>
  <div class="count-to-wrapper">
    <slot name="left" />
    <p class="content-outer">
      <span :id="counterId" :class="['count-to-count-text', countClass]">{{ init }}</span>
      <i :class="['count-to-unit-text', unitClass]">{{ unitText }}</i>
    </p>
    <slot name="right" />
  </div>
</template>

<script>
import './count-to-card.less'
import CountUp from 'countup'

export default {
  name: 'CountTo',
  props: {
    init: {
      type: Number,
      default: 0
    },
    startVal: {
      type: Number,
      default: 0
    },
    end: {
      type: Number,
      required: true
    },
    decimals: {
      type: Number,
      default: 0
    },
    decimal: {
      type: String,
      default: '.'
    },
    duration: {
      type: Number,
      default: 2
    },
    delay: {
      type: Number,
      default: 0
    },
    unEasing: {
      type: Boolean,
      default: false
    },
    useGroup: {
      type: Boolean,
      default: false
    },
    separator: {
      type: String,
      default: ','
    },
    simplify: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    unit: {
      type: Array,
      default() {
        return [
          [3, 'K+'],
          [6, 'M+'],
          [9, 'B+']
        ]
      }
    },
    countClass: {
      type: String,
      default: ''
    },
    unitClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      counter: null,
      unitText: ''
    }
  },
  computed: {
    counterId() {
      return `count_to_${this._uid}`
    }
  },
  watch: {
    end(newVal) {
      let endVal = this.getValue(newVal)
      this.counter.update(endVal)
    }
  },
  mounted() {
    this.$nextTick(() => {
      let endVal = this.getValue(this.end)
      this.counter = new CountUp(this.counterId, this.startVal, endVal, this.decimals, this.duration, {
        useEasing: !this.unEasing,
        useGrouping: this.useGroup,
        separator: this.separator,
        decimal: this.decimal
      })
      setTimeout(() => {
        if (!this.counter.error) this.counter.start()
      }, this.delay)
    })
  },
  methods: {
    getHandleVal(val, len) {
      return {
        endVal: parseInt(val / Math.pow(10, this.unit[len - 1][0])),
        unitText: this.unit[len - 1][1]
      }
    },
    transformValue(val) {
      let len = this.unit.length
      let res = {
        endVal: 0,
        unitText: ''
      }
      if (val < Math.pow(10, this.unit[0][0])) res.endVal = val
      else {
        for (let i = 1; i < len; i++) {
          if (val >= Math.pow(10, this.unit[i - 1][0]) && val < Math.pow(10, this.unit[i][0]))
            res = this.getHandleVal(val, i)
        }
      }
      if (val > Math.pow(10, this.unit[len - 1][0])) res = this.getHandleVal(val, len)
      return res
    },
    getValue(val) {
      let res = 0
      if (this.simplify) {
        let { endVal, unitText } = this.transformValue(val)
        this.unitText = unitText
        res = endVal
      } else {
        res = val
      }
      return res
    }
  }
}
</script>
