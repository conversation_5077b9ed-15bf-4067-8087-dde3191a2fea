<template>
  <div class="fail-table">
    <Table :columns="columns" :data="tableContent" border></Table>
  </div>
</template>

<script>
export default {
  name: 'FailTable',
  props: {
    tableData: {
      type: Object,
      default: () => ({
        head: [],
        content: []
      })
    }
  },
  computed: {
    columns() {
      return this.tableData.head.map((title, index) => ({
        title: title,
        key: 'col' + index,
        align: 'center'
      }))
    },
    tableContent() {
      return this.tableData.content.map((row) => {
        const rowData = {}
        row.forEach((cell, index) => {
          rowData['col' + index] = cell
        })
        return rowData
      })
    }
  }
}
</script>

<style scoped>
.fail-table {
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
