<template>
  <Card>
    <Row>
      <Select v-model="selectProject" style="width: 200px; float: left" @on-change="onChange">
        <Option v-for="item in projectList" :key="item.id" :value="item.id">{{ item.id }} : {{ item.name }}</Option>
      </Select>
      <Button type="primary" :disabled="loading" @click="loadProjectData" style="margin-left: 12px">
        {{ loading ? '请耐心等待加载' : '加载' }}
      </Button>
    </Row>
    <div v-if="selectProject !== null" style="margin-top: 20px">
      <Row v-if="loading">
        <br />
        <Col offset="11" style="margin-bottom: 30px">
          <Spin size="large" style="margin-bottom: 10px" />
          <p>加载中</p>
        </Col>
      </Row>

      <template>
        <question-board
          v-if="Object.keys(questionBoardData).length > 0"
          :value="questionBoardData"
          style="height: 400px; margin-bottom: 30px"
          text="历次问答情况分布雷达图"
        />
      </template>

      <template>
        <div v-for="[type, chartDist] in Object.entries(questionDict)" :key="type">
          <div class="type-header">
            {{ type }}
          </div>
          <div class="chart-container">
            <div v-for="[name, rawValue] in Object.entries(chartDist)" :key="name" class="chart-item">
              <question-chart
                :value="formatValue(rawValue)"
                :text="name + ' 问答情况分布'"
                style="height: 300px; width: 100%"
              />
            </div>
          </div>
        </div>
      </template>

      <template v-for="examDate in examList">
        <statistics-chart
          :key="examDate"
          :value="examChart[examDate].data"
          :value-length="examChart[examDate].dataLength"
          :start-time="examChart[examDate].beginTime"
          style="height: 300px"
          :text="examDate + ' 累计通过情况'"
        />
      </template>

      <template>
        <div style="margin-top: 20px">
          <Row>
            <Input v-model="studentId" placeholder="请输入学号" style="width: 200px; float: left" />
            <Button type="primary" :disabled="loading || !studentId" @click="loadStudentData" style="margin-left: 12px">
              查询学生成绩
            </Button>
          </Row>

          <template v-if="studentScoreData.length > 0">
            <student-score-chart
              :value="studentScoreData"
              style="height: 400px; margin-top: 20px"
              :text="`学生 ${studentId} 的历次成绩变化`"
            />
          </template>
        </div>
      </template>
    </div>
  </Card>
</template>

<script>
import { projectReq } from '@/api/project'
import {
  getProjectChartData,
  getProjectQuestionChartData,
  getProjectAnswerChartData,
  getStudentScoreData
} from '@/api/judge'
import { getErrModalOptions } from '@/libs/util'
import statisticsChart from './statistics-chart'
import questionChart from './question-chart'
import questionBoard from './question-board'
import studentScoreChart from './student-score-chart'

export default {
  name: 'ProjectChartTable',
  components: {
    statisticsChart,
    questionChart,
    questionBoard,
    studentScoreChart
  },
  data() {
    return {
      selectProject: null,
      projectList: null,
      examList: [],
      examChart: {},
      questionDict: {},
      questionBoardData: {},
      loading: false,
      screenWidth: window.innerWidth,
      studentId: '',
      studentScoreData: []
    }
  },
  mounted() {
    this.loadData()
  },
  computed: {
    chartWidth() {
      return Object.keys(this.questionDict).map((type) => {
        const itemCount = Object.keys(this.questionDict[type]).length
        return itemCount ? this.screenWidth / itemCount : this.screenWidth
      })[0]
    }
  },
  methods: {
    loadData() {
      projectReq('get', { page_size: 1000 })
        .then((res) => {
          this.projectList = res.data.models
          this.selectProject = res.data.models[0] ? res.data.models[0].id : null
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onChange(id) {
      this.selectProject = id
    },
    loadProjectData() {
      this.loading = true
      this.examList = []
      this.examChart = {}
      this.questionDict = {}

      getProjectQuestionChartData(this.selectProject)
        .then((res) => {
          const chartData = res.data
          this.questionDict = chartData
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })

      getProjectAnswerChartData(this.selectProject)
        .then((res) => {
          this.questionBoardData = res.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })

      getProjectChartData(this.selectProject)
        .then((res) => {
          const chartData = res.data
          Object.keys(chartData).forEach((examDate) => {
            this.examList.push(examDate)
            this.examChart[examDate] = {}
            this.examChart[examDate].beginTime = chartData[examDate].begin_time
            this.examChart[examDate].data = []
            Object.keys(chartData[examDate]).forEach((key) => {
              if (key !== 'begin_time' && key !== 'total_student' && key !== 'delta') {
                this.examChart[examDate].dataLength = chartData[examDate][key].length
                this.examChart[examDate].data.push({
                  name: key,
                  type: 'line',
                  data: chartData[examDate][key]
                })
              }
            })
          })
          this.examList.sort((a, b) => {
            return new Date(a) < new Date(b) ? 1 : -1
          })
          this.loading = false
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    formatValue(rawValue) {
      const categories = ['A', 'B', 'C', 'F', '无']
      return rawValue
        .map((value, index) => ({
          value: value,
          name: categories[index]
        }))
        .filter((item) => item.value !== 0 && item.name !== '无')
    },
    loadStudentData() {
      this.loading = true
      getStudentScoreData(this.studentId)
        .then((res) => {
          this.studentScoreData = res.data.data
          this.loading = false
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
.type-header {
  text-align: center;
  font-weight: bold;
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.chart-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px; /* 抵消子元素的边距 */
}

.chart-item {
  flex: 0 0 33.333%; /* 每行3个 */
  padding: 0 10px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

@media screen and (max-width: 1200px) {
  .chart-item {
    flex: 0 0 50%; /* 在较小屏幕上每行2个 */
  }
}

@media screen and (max-width: 768px) {
  .chart-item {
    flex: 0 0 100%; /* 在移动设备上每行1个 */
  }
}
</style>
