export const testcaseAbstract = (info) => {
  const parse = JSON.parse(info)
  const universal = parse['universal']
  const result = []
  const mapper = {
    python: 'Python',
    logisim: 'Logisim',
    mars: 'Mips32 ASM',
    ise: 'Verilog HDL',
    vcs: 'Verilog HDL',
    verilog: 'Verilog HDL',
    c: 'C'
  }
  result.push('题目类型：' + (mapper[parse['problem_type'].toLowerCase()] || '未定义'))
  result.push('——————')
  if (parse['problem_type'].toLowerCase() === 'python' || parse['problem_type'].toLowerCase() === 'c') {
    universal['timeout'] && result.push('时间约束：' + universal['timeout'] + 'ms')
    universal['memory'] && result.push('空间约束：' + universal['memory'] + 'KiB')
    universal['length_limit'] && result.push('输出长度上限：' + universal['length_limit'])
  } else if (parse['problem_type'].toLowerCase() === 'mars') {
    universal['instr_limit'] && result.push('指令上限：' + universal['instr_limit'])
  } else {
    result.push('评测方式：' + (universal['is_cpu'] ? 'CPU 评测' : '非 CPU 评测'))
    universal['circ_name'] && result.push('电路名称：' + universal['circ_name'])
    universal['top_module'] && result.push('顶层模块：' + universal['top_module'])
    universal['simulating_time'] && result.push('仿真时间：' + universal['simulating_time'])
    if (universal['is_cpu']) {
      result.push('——————')
      result.push('时间下限：' + (universal['cycle_lower_bound'] ? universal['cycle_lower_bound'] + 'ns' : '0ns'))
      result.push('流水线检测：' + (universal['is_pipeline'] ? '开启' : '未开启'))
    }
  }
  return result.join('\n')
}

export const testcaseName = (name) => {
  // 判断测试点属性，并加上前缀
  if (name[0] === '/') {
    return 'public:\n' + name.slice(1)
  }
  return name.split('/')[0] + ':\n' + name.split('/')[1]
}
