<template>
  <div>
    <Card>
      <filter-table
        ref="tables"
        :data="tableData"
        :columns="columnsWithHandles"
        :handles="handles"
        @on-view="handleView"
        @on-delete="onDelete"
        @on-search="onSearch"
        @on-save-edit="onSaveEdit"
      />
      <Button v-if="withCsv" style="margin: 10px 0" type="primary" @click="exportExcel">导出为 CSV 文件</Button>
      <Page
        :current="curPage"
        :total="totalCnt"
        :page-size="pageSize"
        style="margin: 10px 0"
        show-elevator
        show-total
        @on-change="onCurPageChange"
      />
    </Card>
    <Modal v-model="deleteConfirmVisible" title="确认删除" @on-ok="deleteAfterConfirm">
      {{ deleteConfirmData }}
    </Modal>
  </div>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import FilterTable from '@/view/filter-table/filter-table'
import _ from 'lodash'

export default {
  name: 'TableTemplate',
  components: {
    FilterTable
  },
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    handles: {
      type: Array,
      default: () => ['view', 'delete']
    },
    handleDelete: {
      type: Function
    },
    handleView: {
      type: Function
    },
    handleChange: {
      type: Function
    },
    buttonHandlers: {
      type: Object,
      default() {
        return { view: this.handleView, delete: this.handleDelete }
      }
    },
    getApiData: {
      type: Function,
      required: true,
      default() {
        this.$Modal.error({
          title: `NotImplementedError`,
          content: `Should specify getApiData function`
        })
        return new Promise(() => {
          return null
        })
      }
    },
    apiDataToTableData: {
      type: Function,
      default: (data) => data['data']
    },
    pageSize: {
      type: Number,
      default: 15
    },
    apiDataToTotalCnt: {
      type: Function,
      default: (data) => data['total_count']
    },
    withCsv: {
      type: Boolean,
      default: false
    },
    csvName: {
      type: String,
      default: `exported`
    },
    useCache: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      apiData: {},
      curPage: 1,
      cachedData: {},
      filter: {},
      deleteConfirmVisible: false,
      deleteConfirmData: {}
    }
  },
  computed: {
    tableData() {
      return this.apiDataToTableData(this.apiData) || []
    },
    totalCnt() {
      return this.apiDataToTotalCnt(this.apiData)
    },
    columnsWithHandles() {
      const handles = {
        title: '操作',
        key: 'handle',
        align: 'center'
        // options: this.handles
      }
      return [...this.columns, handles]
    },
    columnQueryOptions() {
      const picked = this.columns.map((item) => {
        const queryOptions = item.queryOptions || {}
        queryOptions.type = queryOptions.type || String
        if (queryOptions.type === String) {
          queryOptions.cmp = queryOptions.cmp || 'contains'
        } else {
          queryOptions.cmp = queryOptions.cmp || 'exact'
        }
        queryOptions.cmp = '__' + queryOptions.cmp
        return { key: item.key, queryOptions }
      })
      const pairs = picked.map((item) => [item.key, item.queryOptions])
      return _.fromPairs(pairs)
    }
  },
  mounted() {
    this.reload()
  },
  methods: {
    async exportExcel() {
      const pageParams = {
        page_size: this.totalCnt
      }
      const params = _.merge(pageParams, this.filter)
      this.getApiData(params)
        .then((res) => {
          const exportData = this.apiDataToTableData(res.data)
          this.$refs.tables.exportCsv({
            filename: `${this.csvName}_${new Date().valueOf()}.csv`,
            columns: this.columns,
            data: exportData
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    onDelete(data) {
      this.deleteConfirmData = this.tableData[data.index]
      this.deleteConfirmVisible = true
    },
    deleteAfterConfirm() {
      this.handleDelete(this.deleteConfirmData)
        .then(() => {
          this.$Notice.success({ title: '删除成功' })
          this.reload()
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    onCurPageChange(val) {
      this.curPage = val
      this.reload()
    },
    reload() {
      const filterParams = this.filter
      const pageParams = {
        page: this.curPage,
        page_size: this.pageSize
      }

      const params = _.merge(pageParams, filterParams)

      this.getApiData(params)
        .then((res) => {
          this.apiData = res.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onSearch(data) {
      const filter = _.mapValues(data, (val, key) => {
        const dataType = this.columnQueryOptions[key].type
        if (dataType === Boolean) {
          return val === 'true' ? 'True' : 'False'
        }
        return dataType(val)
      })

      this.filter = _.mapKeys(filter, (val, key) => key + this.columnQueryOptions[key].cmp)
      this.reload()
    },
    onSaveEdit({ index, newVal }) {
      const oldVal = this.tableData[index]
      this.handleChange(oldVal, newVal)
        .then(() => {
          this.reload()
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    }
  }
}
</script>
