<template>
  <div class="question-preview">
    <!-- 暂无配置状态 -->
    <div v-if="!questionData" class="empty-state">暂无问答题配置</div>

    <!-- 格式错误状态 -->
    <div v-else-if="hasError" class="error-state">
      {{ errorMessage }}
    </div>

    <!-- 正常显示状态 -->
    <div v-else>
      <!-- JSON格式显示 -->
      <div v-if="isJsonFormat" class="json-format">
        <div v-for="(questions, category) in parsedJsonData" :key="category" class="category-group">
          <div class="category-title">{{ category }}</div>
          <div
            v-for="(questionType, questionName) in questions"
            :key="questionName"
            :class="['question-item', getQuestionClass(questionType)]"
          >
            <span class="question-name">{{ questionName }}</span>
            <span :class="['question-type', getQuestionTypeClass(questionType)]">
              {{ getQuestionTypeName(questionType) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 列表格式显示 -->
      <div v-else-if="isListFormat" class="list-format">
        <div v-for="(category, index) in parsedListData" :key="index" class="category-group">
          <div class="category-title">种类 {{ index + 1 }}</div>
          <div v-for="questionName in category" :key="questionName" class="question-item choice-question">
            <span class="question-name">{{ questionName }}</span>
            <span class="question-type choice-type">选择</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionPreview',
  props: {
    questionText: {
      type: String,
      default: ''
    }
  },
  computed: {
    questionData() {
      return this.questionText && this.questionText.trim() !== ''
    },
    parsedData() {
      if (!this.questionData) return null

      try {
        return JSON.parse(this.questionText)
      } catch (error) {
        return null
      }
    },
    hasError() {
      return this.questionData && (!this.parsedData || !this.isValidFormat)
    },
    errorMessage() {
      if (!this.questionData) return ''
      if (!this.parsedData) return 'JSON格式错误，请检查配置'
      if (!this.isValidFormat) return '数据格式错误：应为 {类别: {题目名-是否为填空题} } （1=填空，0=选择）'
      if (!this.isJsonFormat && !this.isListFormat) return '不支持的配置格式'
      return ''
    },
    isValidFormat() {
      if (!this.parsedData) return false
      if (this.isListFormat) return true
      if (this.isJsonFormat) {
        // 验证JSON
        for (const category of Object.values(this.parsedData)) {
          if (typeof category !== 'object' || Array.isArray(category)) return false
          for (const questionValue of Object.values(category)) {
            if (typeof questionValue !== 'number') return false
          }
        }
        return true
      }
      return false
    },
    isJsonFormat() {
      return this.parsedData && typeof this.parsedData === 'object' && !Array.isArray(this.parsedData)
    },
    isListFormat() {
      return this.parsedData && Array.isArray(this.parsedData)
    },
    parsedJsonData() {
      return this.isJsonFormat ? this.parsedData : {}
    },
    parsedListData() {
      return this.isListFormat
        ? this.parsedData.filter((category) => Array.isArray(category) && category.length > 0)
        : []
    }
  },
  methods: {
    getQuestionClass(questionType) {
      if (questionType === 1) {
        return 'blank-question'
      }
      return 'choice-question'
    },
    getQuestionTypeClass(questionType) {
      if (questionType === 1) {
        return 'blank-type'
      }
      return 'choice-type'
    },
    getQuestionTypeName(questionType) {
      if (questionType === 1) {
        return '填空'
      }
      return '选择'
    }
  }
}
</script>

<style scoped>
.question-preview {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.empty-state {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.error-state {
  color: #f56565;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.category-group {
  margin-bottom: 20px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: #fafafa;
}

.category-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: #1890ff;
  font-size: 16px;
}

.question-item {
  margin-bottom: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.question-name {
  color: #333;
  flex: 1;
}

.question-type {
  margin-left: 8px;
  padding: 2px 6px;
  color: white;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
}

.choice-type {
  background: #1890ff;
}

.blank-type {
  background: #52c41a;
}

/* 填空题边框颜色 */
.question-item.blank-question {
  border-left-color: #52c41a;
}

/* 选择题边框颜色（默认） */
.question-item.choice-question {
  border-left-color: #1890ff;
}
</style>
