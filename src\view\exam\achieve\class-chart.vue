<template>
  <div ref="dom" className="charts chart-plot" />
</template>

<script>
import * as echarts from 'echarts'
import { on, off } from '@/libs/tools'
export default {
  name: 'ClassChart',
  props: {
    series: {
      type: Array,
      default: () => []
    },
    totalData: {
      type: Array,
      default: () => []
    },
    teacher: {
      type: Array,
      default: () => []
    },
    animation: {
      type: Boolean,
      default: true
    },
    text: {
      type: String,
      default: ''
    },
    subtext: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dom: null
    }
  },
  watch: {
    series(cur) {
      if (cur && cur.length) {
        setTimeout(this.refresh, 100)
      }
    }
  },
  mounted() {
    this.dom = echarts.init(this.$refs.dom)
    on(window, 'resize', this.resize)
    if (this.series) {
      this.refresh()
    }
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
  },
  methods: {
    resize() {
      this.dom.resize()
    },
    refresh() {
      this.dom.clear()
      this.dom.setOption(this.chartOption())
      this.resize()
    },
    chartOption() {
      return {
        animation: this.animation,
        title: {
          text: this.text,
          subtext: this.subtext,
          top: 0
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          top: 25,
          left: 'center',
          width: '80%',
          textStyle: {
            fontSize: 12
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            var result = params[0].axisValue + '<br/>'
            params.forEach((item) => {
              const totalValue = this.totalData[params[0].dataIndex] || 0
              const actualValue = totalValue === 0 ? 0 : Math.round((item.data * totalValue - 1) / 100)
              const percentage = totalValue === 0 ? 0 : item.data
              result +=
                item.marker + item.seriesName + ': ' + actualValue + ' (' + percentage.toFixed(1) + '%)' + '<br/>'
            })
            return result
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '20%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.teacher
          }
        ],
        yAxis: [
          {
            name: '百分比%',
            type: 'value',
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        toolbox: {
          show: false
        },
        series: this.series
      }
    }
  }
}
</script>
