<template>
  <Card>
    <Card>
      <Row>
        <Col span="6">
          <div class="title">
            <p>
              题目 ID: <a @click="onShow">{{ problem.id }}</a>
            </p>
            <p>题目名称: {{ problem.name }}</p>
          </div>
        </Col>
        <Col span="4" offset="1">
          <i-circle
            :percent="
              problem['total_submission'] === 0
                ? 0
                : Math.round((problem['passed_submission'] / problem['total_submission']) * 100)
            "
            :size="140"
          >
            <div class="circle-custom">
              <h3>提交通过率</h3>
              <p>
                {{
                  problem['total_submission'] === 0
                    ? 0
                    : Math.round((problem['passed_submission'] / problem['total_submission']) * 100) + '%'
                }}
              </p>
              <span>{{ problem['passed_submission'] + '/' + problem['total_submission'] }}</span>
            </div>
          </i-circle>
        </Col>
        <i-circle
          :percent="
            problem.total_student === 0 ? 0 : Math.round((problem.passed_student / problem.total_student) * 100)
          "
          :size="140"
        >
          <div class="circle-custom">
            <h3>学生通过率</h3>
            <p>
              {{
                problem.total_student === 0
                  ? '-'
                  : Math.round((problem.passed_student / problem.total_student) * 100) + '%'
              }}
            </p>
            <span>{{ problem.passed_student + '/' + problem.total_student }}</span>
          </div>
        </i-circle>
      </Row>
    </Card>
    <br />
    <Card>
      <p slot="title">各学院通过率</p>
      <Table :data="departmentData" :columns="departmentColumns" />
    </Card>
    <br />
    <Card>
      <p slot="title">各班级通过率</p>
      <Table :data="classData" :columns="classColumns" />
    </Card>
    <br />
    <Card>
      <p slot="title">Problem Test Case 统计信息</p>
      <Table :data="problem['test_cases']" :columns="testCaseColumns" />
    </Card>
  </Card>
</template>

<script>
import { getProblemStatistics } from '@/api/judge'
import { courseIdReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'
import { userProfileReq } from '@/api/user'
import { Link, PercentTooltip, WhitePre } from '@/libs/render-item'

export default {
  name: 'ProblemStatisticsDetail',
  data() {
    return {
      problem: {},
      testCaseColumns: [
        {
          title: '测试点 ID',
          sortable: true,
          key: 'id',
          render: (h, params) => Link(h, params.row.id, 'testcase_detail')
        },
        {
          title: '测试点名称',
          sortable: true,
          key: 'name',
          render: (h, params) => WhitePre(h, params.row.name)
        },
        {
          title: '提交通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_submission'], params.row['total_submission'])
        },
        {
          title: '学生通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        }
      ],
      departmentData: [],
      departmentColumns: [
        {
          title: '系号',
          render: (h, params) => WhitePre(h, params.row.department)
        },
        {
          title: '学生通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        }
      ],
      classData: [],
      classColumns: [
        {
          title: '教师',
          render: (h, params) => WhitePre(h, params.row.teacher)
        },
        {
          title: '学生通过率',
          render: (h, params) => PercentTooltip(h, params.row['passed_student'], params.row['total_student'])
        }
      ],
      courseCode: null
    }
  },
  mounted() {
    if (this.$store.state.user.userDefaultCourse === -1) {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
            this.onload()
          } else {
            this.$Modal.info({
              title: '请在课程信息/课程总览选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    } else {
      this.onload()
    }
  },
  methods: {
    onload() {
      courseIdReq('get', this.$store.state.user.userDefaultCourse, {})
        .then((res) => {
          this.courseCode = res.data.code
          getProblemStatistics('get', this.$route.params.id, {
            course: this.courseCode
          })
            .then((res) => {
              this.problem = res.data
              Object.keys(res.data['department_specific_count']).forEach((key) => {
                this.departmentData.push({
                  department: key,
                  passed_student: res.data['department_specific_count'][key].passed_student,
                  total_student: res.data['department_specific_count'][key].total_student
                })
              })
              Object.keys(res.data['class_specific_count']).forEach((key) => {
                this.classData.push({
                  teacher: key,
                  passed_student: res.data['class_specific_count'][key].passed_student,
                  total_student: res.data['class_specific_count'][key].total_student
                })
              })
            })
            .catch((error) => {
              this.$Modal.warning(getErrModalOptions(error))
            })
        })
        .catch((error) => {
          this.$Modal.warning(getErrModalOptions(error))
        })
    },
    onShow() {
      this.$router.push({ name: 'problem_detail', params: { id: this.problem.id } })
    }
  }
}
</script>

<style lang="less" scoped>
.circle-custom {
  & h3 {
    padding-top: 2px;
    font-size: 18px;
    font-weight: 500;
    color: black;
  }

  & p {
    color: black;
    font-size: 15px;
    margin: 10px 0 10px;
  }

  & span {
    padding-top: 5px;
    display: block;
    color: #657180;
    font-size: 13px;
    &:before {
      content: '';
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e0e3e6;
      position: relative;
      top: -10px;
    }
  }
}
.title {
  padding-top: 25px;

  & p {
    position: relative;
    left: 10%;
    max-width: 90%;
    font-size: 18px;
    word-break: break-all;
  }
}
</style>
