<template>
  <Row>
    <Col>
      <Card>
        <Form ref="userNew" :model="group" :rules="groupRule" :label-width="120">
          <form-item prop="name" label="组名">
            <Input v-model="group.name" type="text" />
          </form-item>
          <form-item label="配置权限（预设为默认权限）">
            <Transfer
              :list-style="listStyle"
              :data="allRole"
              :target-keys="groupRole"
              :titles="['剩余权限', '现有权限(预设为默认权限)']"
              filterable
              @on-change="handleChange"
            />
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit('userNew')">确认创建</Button>
          </form-item>
        </Form>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { createUserGroup, getUserRole } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'UserGroupCreate',
  data() {
    return {
      group: {
        name: null
      },
      groupRule: {
        name: [{ required: true, message: '请填写组名', trigger: 'blur' }]
      },
      allRole: [],
      groupRole: [],
      listStyle: {
        width: '300px',
        height: '500px'
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      getUserRole()
        .then((res) => {
          this.allRole = res.data.all.map((item) => {
            return {
              key: item,
              label: item
            }
          })
          this.groupRole = res.data.default
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    upload(data) {
      return createUserGroup(data)
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.upload({
            name: this.group.name,
            permission: this.groupRole
          })
            .then(() => {
              this.$Notice.success({ title: '创建成功' })
              this.$router.push({ name: 'group_table' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    },
    handleChange(targetKeys) {
      this.groupRole = targetKeys
    }
  }
}
</script>
