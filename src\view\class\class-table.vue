<template>
  <row>
    <Col v-for="(aClass, index) in classes" :key="aClass.id" span="8">
      <ClassCard
        :name="aClass.name"
        :student-cnt="aClass['student_count']"
        :teacher="aClass.teacher"
        :class-id="aClass.id"
        @delete="onDelete(index)"
      />
    </Col>
    <Col span="8">
      <Card style="width: 350px; margin: 20px; text-align: center" @click.native="createClass">
        <Icon type="ios-add" size="100" />
        <p>创建新班级</p>
      </Card>
    </Col>
  </row>
</template>

<script>
import ClassCard from './class-card'
import { classReq } from '@/api/class'
import { userProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
export default {
  name: 'ClassTable',
  components: {
    ClassCard
  },
  data() {
    return {
      classes: []
    }
  },
  mounted() {
    if (this.$store.state.user.userDefaultCourse === -1) {
      // reload
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
            this.loadData()
          } else {
            this.$Modal.info({
              title: '请在课程信息/课程总览选择当前课程'
            })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    } else {
      this.loadData()
    }
  },
  methods: {
    getClassList() {
      return classReq('get', {
        belong_to: this.$store.state.user.userDefaultCourse
      })
    },
    loadData() {
      this.getClassList()
        .then((res) => {
          this.classes = res.data.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    createClass() {
      this.$router.push({ name: 'class_create' })
    },
    onDelete(number) {
      this.classes = this.classes.filter((element, index) => {
        return index !== number
      })
    }
  }
}
</script>
