<template>
  <Card>
    <p slot="title">
      {{ title }}
    </p>
    <Row type="flex" justify="center" align="middle" class="count-to-page-row">
      <div class="count-to-con">
        <count-to v-if="!disableCountTo" :end="endVal" count-class="count-text" unit-class="unit-class" />
        <div v-if="disableCountTo" class="count-to-con">
          <span class="count-text">
            {{ initEndVal }}
          </span>
        </div>
      </div>
    </Row>
  </Card>
</template>

<script>
import CountTo from '@/components/count-to-card/count-to'

export default {
  name: 'CountToCard',
  components: { CountTo },
  props: {
    title: {
      type: String,
      default: '默认标题'
    },
    initEndVal: {
      type: [Number, String],
      default: 0
    },
    disableCountTo: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      endVal: this.initEndVal
    }
  }
}
</script>

<style lang="less" scoped>
@baseColor: ~'#dc9387';
.count-to-page-row {
  height: 200px;
}

.count-to-con {
  display: block;
  width: 100%;
  text-align: center;
}

.count-text {
  font-size: 100px;
  color: @baseColor;
}

.slot-text {
  font-size: 22px;
}

.unit-class {
  font-size: 30px;
  color: @baseColor;
}
</style>
