<template>
  <div ref="dom" class="charts chart-plot" />
</template>

<script>
import * as echarts from 'echarts'
import { on, off } from '@/libs/tools'
import tdTheme from '@/assets/chart.json'

echarts.registerTheme('tdTheme', tdTheme)

export default {
  name: 'Question<PERSON><PERSON>',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    text: {
      type: String,
      default: ''
    },
    subtext: {
      type: String,
      default: ''
    },
    animation: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dom: null
    }
  },
  watch: {
    value() {
      this.refresh()
    }
  },
  mounted() {
    this.dom = echarts.init(this.$refs.dom, 'tdTheme')
    if (this.value) {
      this.refresh()
    }
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
  },
  methods: {
    resize() {
      this.dom.resize()
    },
    refresh() {
      this.dom.clear()
      this.dom.setOption(this.chartOption())
      on(window, 'resize', this.resize)
    },
    chartOption() {
      const chartWidth = this.dom.getWidth()
      return {
        animation: this.animation,
        title: {
          text: this.text,
          subtext: this.subtext,
          left: 'center',
          textStyle: {
            width: chartWidth * 0.8,
            overflow: 'break',
            lineHeight: 20,
            height: 40,
            fontSize: '14'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: this.value.map((data) => data.name)
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: '50%',
            data: this.value,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    }
  }
}
</script>
