<template>
  <Card>
    <Table :data="data" :columns="columns" />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: left">
        <Button type="primary" @click="onCreate">创建新用户组</Button>
      </div>
      <div style="float: right">
        <Page :total="totalCnt" :current="curPage" :page-size="pageSize" show-elevator @on-change="changePage" />
      </div>
    </div>
  </Card>
</template>

<script>
import { userGroupListReq, deleteUserGroup } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
import { ActionButton, LinkButton, Spacer } from '@/libs/render-item'

export default {
  name: 'UserGroupTable',
  data() {
    return {
      data: [],
      columns: [
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: 'Group Name',
          key: 'name'
        },
        {
          title: 'Action',
          render: (h, params) =>
            h('div', [
              LinkButton(h, params.row.id, 'group_detail', '修改权限', false),
              Spacer(h),
              ActionButton(h, () => this.onDelete(params.row.name), '删除', false)
            ])
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1
    }
  },
  mounted() {
    this.loadData(1)
  },
  methods: {
    loadData(index) {
      userGroupListReq({
        page: index,
        page_size: 10
      })
        .then((res) => {
          this.data = res.data.groups
          this.totalCnt = res.data['item_count']
          this.curPage = res.data['page_now']
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      this.loadData(index)
    },
    onDelete(name) {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          deleteUserGroup({
            name
          })
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.loadData(1)
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onCreate() {
      this.$router.push({ name: 'create_group' })
    }
  }
}
</script>
