<template>
  <card style="width: 350px; margin: 20px">
    <p slot="title">[{{ id }}]{{ name }}</p>
    <Row>
      <Col>
        <p style="text-align: center">前置项目: [{{ parentId }}] {{ parentName }}</p>
      </Col>
      <Col>
        <p style="text-align: center">课程: [{{ courseId }}] {{ courseName }}</p>
      </Col>
    </Row>
    <Row>
      <Col span="6" offset="1" style="text-align: center">
        <p class="operation" @click="onShow">show</p>
      </Col>
      <Col span="6" offset="2" style="text-align: center">
        <p class="operation" @click="onUpdate">update</p>
      </Col>
      <Col span="6" offset="2" style="text-align: center">
        <p class="operation" @click="onDelete">delete</p>
      </Col>
    </Row>
  </card>
</template>

<script>
export default {
  name: 'ProjectCard',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    name() {
      return this.value.name || 'Err NoName'
    },
    id() {
      return this.value.id
    },
    parentId() {
      return this.value['parent_project'] || 'root'
    },
    parentName() {
      return this.value['parent_name'] || ''
    },
    courseId() {
      return this.value['course']
    },
    courseName() {
      return this.value['course__name']
    }
  },
  methods: {
    onShow() {
      this.$Modal.info({
        title: 'show',
        content: this.name
      })
    },
    onDelete() {
      this.$emit('delete')
    },
    onUpdate() {
      this.$emit('update', this.id)
    }
  }
}
</script>

<style lang="less" scoped>
.operation {
  color: #18bc9c;
  background-color: transparent;
}

.operation:hover {
  color: #0f7864;
  text-decoration: underline;
}
</style>
