<template>
  <Row>
    <Col span="8" offset="8">
      <card>
        课上测试举手
        <Row>
          <Col span="12">
            学号:
            <Input v-model="testHandup_onExam.student_id" />
            房间id(注意不是房间号):
            <Input v-model="testHandup_onExam.room_id" />
            座位id(注意不是座位号):
            <Input v-model="testHandup_onExam.seat_id" />
            PIE id:
            <Input v-model="testHandup_onExam.pie_id" />
          </Col>
        </Row>
        <br />
        <Button @click="onSetHandupAble">设置为可举手状态</Button>
      </card>
    </Col>
  </Row>
</template>

<script>
import { testHandUpInExamReq } from '@/api/test'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'TestHandup',
  data() {
    return {
      testHandup_onExam: {
        student_id: '',
        room_id: '',
        seat_id: '',
        pie_id: ''
      }
    }
  },
  methods: {
    onSetHandupAble() {
      const params = {
        ...this.testHandup_onExam
      }
      testHandUpInExamReq(params)
        .then(() => {
          this.$Notice.success({ title: '举手成功' })
        })
        .catch((err) => this.$Modal.error(getErrModalOptions(err)))
    }
  }
}
</script>
