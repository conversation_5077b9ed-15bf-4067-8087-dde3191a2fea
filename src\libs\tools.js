import store from '@/store/index.js'

export const setToken = (token) => {
  store.commit('user/setToken', token)
}

export const setRefreshToken = (token) => {
  store.commit('user/setRefreshToken', token)
}

export const getToken = () => {
  return store.getters['user/token']
}

export const getRefreshToken = () => {
  return store.getters['user/refreshToken']
}

export const hasChild = (item) => {
  return item.children && item.children.length !== 0
}

export const getUnion = (arr1, arr2) => {
  return Array.from(new Set([...arr1, ...arr2]))
}

export const hasOneOf = (targetArr, arr) => {
  return targetArr.some((_) => arr.indexOf(_) > -1)
}

export const doCustomTimes = (times, callback) => {
  let i = -1
  while (++i < times) {
    callback(i)
  }
}

export const on = (() => {
  if (document.addEventListener) {
    return (element, event, handler) => {
      if (element && event && handler) {
        element.addEventListener(event, handler, false)
      }
    }
  } else {
    return (element, event, handler) => {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler)
      }
    }
  }
})()

export const off = (() => {
  if (document.removeEventListener) {
    return (element, event, handler) => {
      if (element && event) {
        element.removeEventListener(event, handler, false)
      }
    }
  } else {
    return (element, event, handler) => {
      if (element && event) {
        element.detachEvent('on' + event, handler)
      }
    }
  }
})()

export const objEqual = (obj1, obj2) => {
  const keysArr1 = Object.keys(obj1)
  const keysArr2 = Object.keys(obj2)
  if (keysArr1.length !== keysArr2.length) return false
  if (keysArr1.length === 0 && keysArr2.length === 0) return true
  return !keysArr1.some((key) => obj1[key] !== obj2[key])
}
