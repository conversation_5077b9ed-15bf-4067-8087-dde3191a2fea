<template>
  <div>
    <Card dis-hover>
      <p slot="title" style="font-size: 20px">详细信息</p>
      <template v-slot:extra>
        <Button
          class="push-msg-show-button"
          type="primary"
          icon="md-send"
          :loading="pushing"
          :disabled="pushMsgInfo.status === 1"
          @click="onPush"
        >
          推送
        </Button>
        <Button
          class="repush-msg-show-button"
          type="primary"
          icon="md-send"
          :loading="pushing"
          :disabled="pushMsgInfo.status === 1 || pushMsgInfo.status === 0"
          @click="onRePush"
        >
          一键重推
        </Button>
        <Button
          class="push-msg-show-button"
          type="warning"
          icon="md-create"
          :disabled="pushMsgInfo.status !== 0"
          @click="onEdit"
        >
          修改
        </Button>
      </template>
      <div>
        <Row v-for="(value, key) in pushMsgVisableDetail" :key="key">
          <Col span="5" style="font-size: 16px">
            <strong>{{ key }}</strong>
          </Col>
          <Col offset="12" style="font-size: 16px">
            <div class="text-wrapper">
              {{ value }}
            </div>
          </Col>
        </Row>
        <Row v-if="pushMsgInfo.status !== undefined">
          <Col span="5" style="font-size: 16px">
            <strong>当前状态</strong>
          </Col>
          <Col offset="12" style="font-size: 18px">
            <Tag :color="pushStatus[pushMsgInfo.status].color">
              {{ pushStatus[pushMsgInfo.status].name }}
            </Tag>
          </Col>
        </Row>
      </div>
    </Card>
    <br />
    <Card dis-hover>
      <p slot="title" style="font-size: 20px">推送内容</p>
      <div class="text-wrapper">
        <div v-html="pushMsgInfo.content" style="white-space: normal"></div>
      </div>
    </Card>
    <br />
    <Card dis-hover>
      <p slot="title" style="font-size: 20px">推送情况</p>
      <div>
        <Table
          ref="table"
          :highlight-row="true"
          size="large"
          :columns="columns"
          :data="pushMsgInfo['recipients_status']"
        />
      </div>
    </Card>
  </div>
</template>

<script>
import { pushMsgDetailReq, rePushMsgDetailReq } from '@/api/push-message'
import _ from 'lodash'
import { getErrModalOptions, getLocalTime } from '@/libs/util'
import { TagByObj } from '@/libs/render-item'

const pushStatus = {
  0: {
    value: 0,
    name: '未推送',
    color: 'yellow'
  },
  1: {
    value: 1,
    name: '推送成功',
    color: 'green'
  },
  2: {
    value: 2,
    name: '部分推送失败',
    color: 'red'
  }
}

const msgPushStatus = {
  0: {
    value: 0,
    name: '未推送',
    color: 'yellow'
  },
  1: {
    value: 1,
    name: '收到确认',
    color: 'green'
  },
  2: {
    value: 2,
    name: '推送成功',
    color: 'blue'
  },
  3: {
    value: 3,
    name: '推送失败',
    color: 'red'
  }
}

const pushMsgVisibleFields = [
  ['id', 'ID'],
  ['title', '标题'],
  ['comment', '备注'],
  ['with_web_notification', '使用课程网站推送'],
  ['with_email', '使用邮件推送'],
  ['created_at', '创建时间'],
  ['created_by__username', '创建者'],
  ['pushed_at', '最后一次推送时间']
]

const tableColumns = [
  {
    title: '学号',
    sortable: true,
    key: 'student__student_id'
  },
  {
    title: '姓名',
    sortable: true,
    key: 'student__name'
  },
  {
    title: '电子邮件',
    key: 'student__user__email'
  }
]

const tableColumnsWebNotificationExtension = [
  {
    title: '课程网站通知状态',
    key: 'notification_status',
    sortable: true,
    render: (h, params) => TagByObj(h, msgPushStatus[params.row['notification_status']])
  },
  {
    title: '课程网站确认时间',
    key: 'notification_confirmed_at',
    sortable: true
  }
]

const tableColumnsEmailExtension = [
  {
    title: 'Email 通知状态',
    key: 'email_status',
    sortable: true,
    render: (h, params) => TagByObj(h, msgPushStatus[params.row['email_status']])
  }
]

export default {
  name: 'PushMessageShow',
  data() {
    return {
      pushStatus: pushStatus,
      columns: tableColumns,
      pushMsgVisableDetail: {},
      pushMsgInfo: {},
      pushing: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      pushMsgDetailReq('get', this.$route.params.id, {})
        .then((res) => {
          this.pushMsgInfo = _.omit(res.data, 'test_case_judge_record')
          // 抽出需要显示的信息并转换名称
          pushMsgVisibleFields.forEach((kvpair) => {
            let key = kvpair[0]
            let val = kvpair[1]
            this.pushMsgVisableDetail[val] = this.pushMsgInfo[key]
          })
          let newTableCol = tableColumns
          // 如果需要网站推送 为表格添加网站推送状态列
          if (this.pushMsgInfo.with_web_notification) {
            newTableCol = newTableCol.concat(tableColumnsWebNotificationExtension)
          }
          // 如果需要邮件推送 为表格添加邮件列
          if (this.pushMsgInfo.with_email) {
            newTableCol = newTableCol.concat(tableColumnsEmailExtension)
          }
          this.columns = newTableCol
          // 修正日期显示
          this.pushMsgInfo['recipients_status'].forEach((rec_info) => {
            rec_info.notification_confirmed_at = getLocalTime(rec_info.notification_confirmed_at)
            rec_info.email_confirmed_at = getLocalTime(rec_info.email_confirmed_at)
          })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onPush() {
      this.$Modal.confirm({
        title: '确认推送 <' + this.pushMsgInfo.title + '>, id = ' + this.pushMsgInfo.id + ' ?',
        onOk: () => {
          this.pushing = true
          const reqData = { do_push: true }
          pushMsgDetailReq('post', this.pushMsgInfo.id, reqData)
            .then((res) => {
              this.pushing = false
              if (res.data.status === 1) {
                this.$Notice.success({ title: '推送信息成功' })
              } else {
                this.$Modal.warning({ title: '部分推送信息失败: ', content: res.data['failure_list'].join('<br>') })
              }
              this.loadData()
            })
            .catch((error) => {
              this.pushing = false
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onRePush() {
      this.$Modal.confirm({
        title: '确认一键重推 <' + this.pushMsgInfo.title + '>, id = ' + this.pushMsgInfo.id + ' ?',
        onOk: () => {
          this.pushing = true
          const reqData = { redo_push: true }
          rePushMsgDetailReq('post', this.pushMsgInfo.id, reqData)
            .then((res) => {
              this.pushing = false
              if (res.data.status === 1) {
                this.$Notice.success({ title: '推送信息成功' })
              } else {
                this.$Modal.warning({ title: '部分推送信息失败: ', content: res.data['failure_list'].join('<br>') })
              }
              this.loadData()
            })
            .catch((error) => {
              this.pushing = false
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    },
    onEdit() {
      this.$router.push({ name: 'push_message_detail', params: { id: this.pushMsgInfo.id } })
    }
  }
}
</script>

<style lang="less" scoped>
.push-card-header {
  display: flex;
  justify-content: space-between;
}

.text-wrapper {
  white-space: pre-wrap;
}

.push-msg-show-button {
  margin-right: 5px;
}

.repush-msg-show-button {
  margin-right: 5px;
}
</style>
