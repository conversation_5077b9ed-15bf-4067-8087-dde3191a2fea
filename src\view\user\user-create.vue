<template>
  <Row>
    <Col>
      <Card>
        <Form ref="userNew" :model="user" :rules="userRule" :label-width="120">
          <Row>
            <Col span="8">
              <form-item prop="email" label="邮箱">
                <Input v-model="user.email" type="text" />
              </form-item>
              <form-item prop="username" label="用户名">
                <Input v-model="user.username" type="text" />
              </form-item>
              <form-item prop="newPassword1" label="新密码">
                <Input v-model="user.newPassword1" type="password" />
              </form-item>
              <form-item prop="newPassword2" label="再次输入新密码">
                <Input v-model="user.newPassword2" type="password" />
              </form-item>
              <form-item prop="fullName" label="用户姓名">
                <Input v-model="user.fullName" type="text" />
              </form-item>
              <form-item label="当前课程" prop="nowCourse">
                <Select v-model="user.nowCourse" clearable @on-change="onChange">
                  <Option v-for="course in allCourse" :key="course.id" :value="course.id">
                    {{ course.name }}
                  </Option>
                </Select>
              </form-item>
              <form-item prop="roleName" label="课程角色">
                <Input v-model="user.roleName" type="text" />
              </form-item>
            </Col>
            <Col span="16">
              <form-item label="有权限课程" prop="authorizedCourses">
                <Select v-model="user.authorizedCourses" multiple style="width: 85%">
                  <Option v-for="course in allCourse" :key="course.id" :value="course.id">
                    {{ course.name }}
                  </Option>
                </Select>
              </form-item>
              <form-item label="设置分组">
                <Transfer
                  :list-style="listStyle"
                  :data="allGroups"
                  :target-keys="userGroups"
                  :titles="['可用分组', '当前分组']"
                  filterable
                  @on-change="handleGroupChange"
                />
              </form-item>
              <form-item label="配置权限">
                <Transfer
                  :list-style="listStyle"
                  :data="allRole"
                  :target-keys="userRole"
                  :titles="['剩余权限', '现有权限(预设为默认权限)']"
                  filterable
                  @on-change="handleChange"
                />
              </form-item>
              <form-item>
                <Button type="primary" @click="handleSubmit('userNew')">确认创建</Button>
              </form-item>
            </Col>
          </Row>
        </Form>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { createUser, authorizedCoursesReq, getUserRole, userGroupListReq, userIdProfileReq } from '@/api/user'
import { getErrModalOptions } from '@/libs/util'
import { courseReq } from '@/api/course'

export default {
  name: 'UserCreate',
  data() {
    return {
      user: {
        email: null,
        username: null,
        newPassword1: null,
        newPassword2: null,
        nowCourse: null,
        authorizedCourses: [],
        fullName: null,
        roleName: null
      },
      userRule: {
        email: [
          { required: true, message: '请填写邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        username: [
          { required: true, message: '请填写用户名', trigger: 'blur' },
          { min: 3, message: '用户名过短', trigger: 'blur' },
          { max: 18, message: '用户名过长', trigger: 'blur' },
          {
            pattern: /^(?!\d+$)[\da-z_]*$/,
            message: '用户名只能包含小写字母，数字，下划线，且不能为纯数字',
            trigger: 'blur'
          }
        ],
        newPassword1: [
          { required: true, message: '请填写密码', trigger: 'blur' },
          { min: 6, message: '请至少输入6位密码', trigger: 'blur' },
          { max: 18, message: '密码最多18位', trigger: 'blur' }
        ],
        newPassword2: [
          { required: true, message: '请再次填写密码', trigger: 'blur' },
          { validator: this.validatePassWord, trigger: 'blur' },
          { min: 6, message: '请至少输入6位密码' }
        ],
        fullName: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
        nowCourse: [{ type: 'number', required: true, message: '请选择当前课程', trigger: 'blur' }],
        roleName: [{ required: true, message: '请输入用户角色', trigger: 'blur' }]
      },
      allRole: [],
      allGroups: [],
      userRole: [],
      userGroups: [],
      allCourse: [],
      listStyle: {
        width: '300px'
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      try {
        await getUserRole().then((res) => {
          this.allRole = res.data.all.map((item) => {
            return {
              key: item,
              label: item
            }
          })
          this.userRole = res.data.default
        })
        await userGroupListReq().then((res) => {
          this.allGroups = res.data.groups.map(({ id, name }) => ({
            key: id,
            label: name
          }))
        })
        await courseReq('get', {}).then((res) => {
          this.allCourse = res.data.data
        })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    validatePassWord(rule, value, callback) {
      if (value === '') {
        return callback(new Error('请再次输入密码'))
      } else if (value !== this.user.newPassword1) {
        return callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    },
    upload(data) {
      return createUser(data)
    },
    async handleSubmit(name) {
      try {
        await new Promise((resolve, reject) =>
          this.$refs[name].validate((valid) => (valid ? resolve() : reject(new Error('提交的数据有误'))))
        )
        const res = await this.upload({
          email: this.user.email,
          username: this.user.username,
          password: this.user.newPassword2,
          permission: this.userRole,
          groups: this.userGroups,
          first_name: this.user.fullName,
          last_name: this.user.roleName
        })
        await userIdProfileReq('put', res.data.id, { course_id: this.user.nowCourse })
        await authorizedCoursesReq('put', res.data.id, { courses: this.user.authorizedCourses })
        this.$Modal.info({ title: `成功创建，ID 为 ${res.data.id}` })
        await this.$router.push({ name: 'user_table' })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    handleChange(targetKeys) {
      this.userRole = targetKeys
    },
    handleGroupChange(targetKeys) {
      this.userGroups = targetKeys
    },
    onChange(id) {
      this.user.nowCourse = id
    }
  }
}
</script>
