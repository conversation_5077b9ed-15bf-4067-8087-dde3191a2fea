import Vue from 'vue'

import View<PERSON> from 'view-design'
import VOrgTree from 'v-org-tree'
import TreeTable from 'tree-table-vue'
import mavonEditor from 'novam-editor'
import VueClipboard from 'vue-clipboard2'
import vClickOutside from 'v-click-outside'

import 'v-org-tree/dist/v-org-tree.css'
import 'novam-editor/dist/css/index.css'
import 'view-design/dist/styles/iview.css'

import App from './App'
import store from './store'
import router from './router'

import './index.less'
import './assets/icons/iconfont.css'

Vue.config.productionTip = false

Vue.use(ViewUI)
Vue.use(VOrgTree)
Vue.use(TreeTable)
Vue.use(mavonEditor)
Vue.use(VueClipboard)
Vue.use(vClickOutside)

new Vue({
  el: '#app',
  store,
  router,
  render: (h) => h(App)
})
