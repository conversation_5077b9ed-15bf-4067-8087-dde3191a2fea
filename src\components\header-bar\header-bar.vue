<template>
  <div class="header-bar">
    <sider-trigger :collapsed="collapsed" icon="md-menu" @on-change="handleCollapsedChange" />
    <custom-bread-crumb :list="breadCrumbList" show-icon style="margin-left: 30px" />
    <div class="custom-content-con">
      <slot />
    </div>
  </div>
</template>

<script>
import './header-bar.less'
import siderTrigger from './sider-trigger'
import customBreadCrumb from './custom-bread-crumb'

export default {
  name: 'HeaderBar',
  components: {
    siderTrigger,
    customBreadCrumb
  },
  props: {
    collapsed: Boolean
  },
  computed: {
    breadCrumbList() {
      return this.$store.state.app.breadCrumbList
    }
  },
  methods: {
    handleCollapsedChange(state) {
      this.$emit('on-coll-change', state)
    }
  }
}
</script>
