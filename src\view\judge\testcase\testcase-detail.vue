<template>
  <Card>
    <Card>
      <p slot="title">Test Case 详情</p>
      <Form ref="testcaseUpdate" :model="testcaseUpdate" :label-width="100">
        <form-item prop="name" label="测试点名称">
          <Input v-model="testcaseUpdate.name" type="text" />
        </form-item>
        <form-item prop="judge_parameter" label="Parameter">
          <Input
            v-model="testcaseUpdate.judge_parameter"
            :autosize="{ minRows: 2 }"
            type="textarea"
            style="white-space: pre-wrap"
          />
        </form-item>
        <form-item prop="description" label="测试点描述">
          <Input v-model="testcaseUpdate.description" :rows="4" type="textarea" />
        </form-item>
      </Form>
    </Card>
    <br />
    <Row>
      <Col span="7">
        <Button :disabled="downloadButtonDisable" type="primary" style="margin-left: 10px" @click="handleDownload">
          {{ downloadButtonText }}
        </Button>
      </Col>
    </Row>
  </Card>
</template>

<script>
import { testcaseIdReq, testcaseFileReq } from '@/api/judge'
import { getErrModalOptions, processRemoteDownload } from '@/libs/util'

export default {
  name: 'TestCaseUpdate',
  data() {
    return {
      testcaseUpdate: {}
    }
  },
  computed: {
    downloadButtonDisable() {
      return this.testcaseUpdate['judge_data'] === null
    },
    downloadButtonText() {
      return '下载 ' + (this.testcaseUpdate['judge_data__filename'] || '无文件')
    },
    trueTestcaseUpdate() {
      return {
        name: this.testcaseUpdate.name,
        judge_parameter: this.testcaseUpdate.judge_parameter,
        description: this.testcaseUpdate.description
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    update(data) {
      return testcaseIdReq('put', this.$route.params.id, data)
    },
    loadData() {
      testcaseIdReq('get', this.$route.params.id, {})
        .then((res) => {
          this.testcaseUpdate = res.data
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    async handleDownload() {
      try {
        const res = await testcaseFileReq('get', this.$route.params.id)
        processRemoteDownload(res)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    }
  }
}
</script>
