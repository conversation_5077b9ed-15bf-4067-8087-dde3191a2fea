<template>
  <card style="width: 350px; margin: 20px">
    <p slot="title" style="margin-top: 4px; margin-bottom: 4px">{{ name }} : {{ teacher }}</p>
    <Row>
      <Col style="margin-bottom: 20px">
        <p style="text-align: center">学生人数: {{ studentCnt }}</p>
      </Col>
    </Row>
    <Row style="text-align: center; margin-bottom: 10px; display: flex" justify="center">
      <Col span="11">
        <p class="operation" @click="onShow">管理</p>
      </Col>
      <Col span="2">|</Col>
      <Col span="11">
        <p class="operation" @click="onDelete">删除</p>
      </Col>
    </Row>
  </card>
</template>

<script>
import { classIdReq } from '@/api/class'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'ClassCard',
  props: {
    name: {
      type: String,
      default: 'xxx 班'
    },
    studentCnt: {
      type: Number,
      default: 50
    },
    classId: {
      type: Number,
      default: 1111
    },
    teacher: {
      type: String,
      default: 'xxx'
    }
  },
  methods: {
    onShow() {
      this.$router.push({ name: 'class_show', params: { id: this.classId } })
    },
    onDelete() {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          classIdReq('delete', this.classId, {})
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.$emit('delete')
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        },
        onCancel: () => {}
      })
    }
  }
}
</script>

<style lang="less" scoped>
.operation {
  color: #18bc9c;
  background-color: transparent;
}

.operation:hover {
  color: #0f7864;
  text-decoration: underline;
}
</style>
