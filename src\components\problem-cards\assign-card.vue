<template>
  <Card>
    <p slot="title">PTA 验题任务分配</p>
    <Form>
      <div>
        <!-- 多选用户 -->
        <div v-if="userList.length > 0">
          <h3 style="margin-top: 1em; margin-bottom: 1em">选择要分派的助教：</h3>
          <Select v-model="selectedUserIds" multiple style="width: 75%">
            <Option v-for="user in userList" :key="user.id" :value="user.id" :disabled="user.disabled">
              {{ user.username }}
            </Option>
          </Select>
          <Button type="primary" @click="assignAssistants" :disabled="!selectedUserIds.length">分配验题任务</Button>
        </div>
        <div v-else>
          <h3 style="margin-top: 1em; margin-bottom: 1em">当前课程下没有助教</h3>
        </div>

        <!-- PTA 列表 -->
        <div v-if="ptaList.length" style="margin-top: 1em">
          <h3 style="margin-top: 1em; margin-bottom: 1em">当前已分配的验题任务</h3>
          <Button type="primary" @click="updatePta">更新完成情况</Button>
          <Table :columns="ptaColumns" :data="ptaList" :border="true" :stripe="true" style="margin-top: 1em">
            <template slot-scope="{ row }" slot="statusSlot">
              <span>{{ row.complete ? '已完成' : '未完成' }}</span>
            </template>
            <template slot-scope="{ row }" slot="actionSlot">
              <Button type="error" size="small" style="margin-right: 8px" @click="deletePTA(row.ptaid)"> 删除 </Button>
              <Button
                v-if="!row.complete"
                type="success"
                size="small"
                style="margin-right: 8px"
                @click="completePta(row.ptaid)"
              >
                标记完成
              </Button>
              <Button v-else type="warning" size="small" style="margin-right: 8px" @click="uncompletePta(row.ptaid)">
                标记未完成
              </Button>
            </template>
            <template slot-scope="{ row }" slot="noteActionSlot">
              <Button type="primary" size="small" @click="editNote(row)"> 编辑原因 </Button>
            </template>
          </Table>
        </div>
      </div>

      <Modal v-model="showNoteModal" title="填写标记完成原因" @on-ok="submitNote">
        <Input v-model="noteInput" type="textarea" placeholder="请输入未完成的原因或备注" :rows="4" />
      </Modal>
    </Form>
  </Card>
</template>

<script>
import { userProfileReq } from '@/api/user.js'
import { assistantReq } from '@/api/course.js'
import { ProblemToAssistantReq, updatePtaReq, completePtaReq, uncompletePtaReq, writeReasonReq } from '@/api/judge.js'

export default {
  name: 'PtaAssignCard',
  props: {
    problemId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      userList: [],
      selectedUserIds: [],
      ptaList: [],
      noteInput: '',
      showNoteModal: false,
      currentSubmitId: null
    }
  },
  computed: {
    ptaColumns() {
      return [
        {
          title: '助教用户名',
          key: 'username',
          minWidth: 150
        },
        {
          title: '状态',
          slot: 'statusSlot',
          minWidth: 100
        },
        {
          title: '操作',
          slot: 'actionSlot',
          minWidth: 200
        },
        {
          title: '未及时完成原因或备注',
          key: 'note',
          minWidth: 200
        },
        {
          title: '操作备注',
          slot: 'noteActionSlot',
          minWidth: 200
        }
      ]
    }
  },
  mounted() {
    this.loadPTAList()
    this.loadAssistants()
  },
  methods: {
    async loadAssistants() {
      try {
        const res = await userProfileReq('get', {})
        const curCourse = res.data.course.id
        const asl = await assistantReq('get', curCourse, {})
        this.userList = asl.data.data
      } catch (err) {
        console.error('加载当前课程下助教列表失败', err)
        this.$Message.error('加载助教列表失败，请刷新重试')
      }
    },
    async loadPTAList() {
      try {
        const res = await ProblemToAssistantReq('get', this.problemId)
        this.ptaList = res.data.ptas

        const currentUserIds = this.userList.map((user) => user.id)
        const assignedUserIds = this.ptaList.map((pta) => pta.userid)

        // 分配了验题，但不是本课程，无法被取消
        const disableAssistants = this.ptaList.filter((pta) => !currentUserIds.includes(pta.userid))

        for (const pta of disableAssistants) {
          if (!this.userList.some((user) => user.id === pta.userid)) {
            this.userList.push({
              id: pta.userid,
              username: `${pta.username}`,
              disabled: true
            })
          }
        }

        // 默认选中已分配的助教
        this.selectedUserIds = assignedUserIds
      } catch (err) {
        console.error('加载验题助教列表失败', err)
        this.$Message.error('加载验题任务列表失败，请刷新重试')
      }
    },
    async assignAssistants() {
      try {
        const res = await ProblemToAssistantReq('post', this.problemId, {
          assistant_ids: this.selectedUserIds
        })
        const { created, skipped } = res.data
        const successMsg = `成功分配：${created.join(', ')}` + (skipped.length ? `；已催促：${skipped.join(', ')}` : '')
        this.$Message.success({
          content: successMsg,
          duration: 4
        })
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('分配失败，请检查网络或权限')
      }
    },
    async deletePTA(ptaId) {
      try {
        await ProblemToAssistantReq('delete', ptaId)
        this.$Message.success('删除成功')
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('删除失败，请重试')
      }
    },
    async submitNote() {
      if (this.currentSubmitId === null) return
      try {
        await writeReasonReq(this.currentSubmitId, {
          note: this.noteInput
        })
        this.$Message.success('原因提交成功')
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('原因提交失败，请重试')
      } finally {
        this.showNoteModal = false
        this.currentSubmitId = null
      }
    },
    async completePta(ptaId) {
      try {
        await completePtaReq(ptaId)
        this.$Message.success('标记完成成功')
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('标记完成失败，请重试')
      }
    },
    async uncompletePta(ptaId) {
      try {
        await uncompletePtaReq(ptaId)
        this.$Message.success('标记未完成成功')
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('标记未完成失败，请重试')
      }
    },
    editNote(row) {
      this.noteInput = row.note
      this.currentSubmitId = row.ptaid
      this.showNoteModal = true
    },
    async updatePta() {
      try {
        await updatePtaReq(this.problemId, {})
        this.$Message.success('更新完成')
        await this.loadPTAList()
      } catch (err) {
        console.error(err)
        this.$Message.error('更新失败，请重试')
      }
    }
  }
}
</script>
