<template>
  <div>
    <Modal v-model="showUploadModal" title="修改座位信息" @on-ok="upload">
      <Form ref="formInline" :model="seatForm" :rules="seatRule" inline>
        <FormItem prop="name">
          <Input v-model="seatForm.name" type="text" placeholder="Seat Name" />
        </FormItem>
        <FormItem prop="comment">
          <Input v-model="seatForm.comment" type="text" placeholder="Seat Comment" />
        </FormItem>
        <FormItem prop="available">
          <i-switch v-model="seatForm.available" size="large">
            <span slot="open">On</span>
            <span slot="close">Off</span>
          </i-switch>
        </FormItem>
      </Form>
    </Modal>
    <launchpad :value="launchpadValue" @on-click="onClick" />
    <Table v-show="false" ref="tables" />
    <Button style="margin: 10px 0" type="success" @click="onExportSeats">导出 CSV</Button>
  </div>
</template>

<script>
import Launchpad from '@/components/launchpad'
import { roomReqWithId, seatReqWithId } from '@/api/room'
import { getErrModalOptions } from '@/libs/util'
import _ from 'lodash'

export default {
  name: 'RoomEditing',
  components: { Launchpad },
  data() {
    const posRule = {
      type: 'integer',
      min: 1,
      message: 'should be positive',
      trigger: 'blur',
      transform(v) {
        return Number(v)
      }
    }

    return {
      showUploadModal: false,
      roomId: this.$route.params.id,
      seats: [],
      seatForm: {},
      room: '',
      seatRule: {
        name: [{ required: true, message: 'Please fill in the seat name', trigger: 'blur' }],
        pos_x: [{ required: false, message: 'pos_x required', trigger: 'blur' }, posRule],
        pos_y: [{ required: false, message: 'pos_y required', trigger: 'blur' }, posRule]
      }
    }
  },
  computed: {
    row() {
      return _.max(_.map(this.seats, (item) => item.pos_x))
    },
    col() {
      return _.max(_.map(this.seats, (item) => item.pos_y))
    },
    launchpadValue() {
      const row = this.row
      const col = this.col
      const length = row * col
      const data = _.times(length, { text: '', type: 'default' })
      _.forEach(this.seats, (item) => {
        const { name, pos_x, pos_y, available } = item
        const ind = (pos_x - 1) * col + (pos_y - 1)
        data[ind] = {
          meta: _.cloneDeep(item),
          text: name,
          type: available ? 'primary' : 'warning'
        }
      })
      return { col, row, data }
    }
  },
  mounted() {
    this.reload()
  },
  methods: {
    onClick({ index }) {
      const curSeat = this.launchpadValue.data[index].meta
      if (!curSeat) {
        return
      }
      this.seatForm = curSeat
      this.seatForm.pos_x = String(this.seatForm.pos_x)
      this.seatForm.pos_y = String(this.seatForm.pos_y)
      this.showUploadModal = true
    },
    reload() {
      roomReqWithId('get', this.roomId)
        .then((res) => {
          this.seats = res.data.seats
          this.room = res.data
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    upload() {
      const rel = this
      const params = _.pick(rel.seatForm, ['name', 'comment', 'available'])
      this.$Modal.confirm({
        title: 'upload',
        content: `${JSON.stringify(params, null, 2)}`,
        onOk: async () => {
          try {
            await seatReqWithId('put', rel.seatForm.id, params)
            this.reload()
            this.$Notice.success({ title: '上传成功' })
          } catch (err) {
            await new Promise((resolve) => setTimeout(resolve, 500))
            rel.$Modal.error(getErrModalOptions(err))
          }
        }
      })
    },
    onExportSeats() {
      this.$refs.tables.exportCsv({
        filename: `room_${this.room.name}.csv`,
        columns: [
          { key: 'id' },
          { key: 'pos_x' },
          { key: 'pos_y' },
          { key: 'name' },
          { key: 'comment' },
          { key: 'available' }
        ],
        data: this.seats
      })
    }
  }
}
</script>
