<template>
  <Card>
    <div style="width: 100%; height: 100px; display: inline-block; position: relative" v-if="!ready">
      <Spin size="large" fix></Spin>
    </div>
    <detail-card
      v-if="ready"
      ref="detailHolder"
      :interval="problem.interval"
      :max-submission="problem.maxSubmission"
      :name="problem.name"
      :description="problem.description"
      :type="problem.type"
      :reset-time="problem.resetTime"
      :explain="problem.explain"
    />
    <attachment-card
      v-if="ready"
      ref="attachmentHolder"
      :problem-data="problem.problemData"
      :problem-file="problemFile"
      :problem-filename="problem.problemFilename"
      style="margin-top: 16px"
    />
    <assign-card v-if="ready" :problem-id="problem.id" />
    <component
      v-if="ready"
      ref="answerHolder"
      :is="mapProblemType"
      :type="problem.type"
      :answer="problem.answer"
      :choices="problem.choices"
      :testcases="problem.testcases"
      :testcase-file="testcaseFile"
      :testcase-abstract="testcaseAbstract"
      style="margin-top: 16px"
    />
  </Card>
</template>

<script>
import moment from 'moment'
import { judgeProblemIdReq, problemFileReq, testcaseFileReq } from '@/api/judge'
import { getErrModalOptions } from '@/libs/util'
import { testcaseAbstract } from '@/libs/testcases'
import {
  AttachmentCard,
  BlankCard,
  ChoicesCard,
  DetailCard,
  TestcaseCard,
  AssignCard
} from '@/components/problem-cards'

export default {
  name: 'ProblemDetail',
  components: { AttachmentCard, BlankCard, ChoicesCard, DetailCard, TestcaseCard, AssignCard },
  data() {
    return {
      problem: {
        interval: 30,
        maxSubmission: 1,
        name: '',
        description: '',
        type: null,
        resetTime: new Date().toISOString()
      },
      ready: false
    }
  },
  computed: {
    mapProblemType() {
      switch (this.problem.type) {
        case '0':
          return 'testcaseCard'
        case '1':
        case '2':
          return 'choicesCard'
        case '3':
          return 'blankCard'
        default:
          return ''
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.ready = false
      try {
        const res = await judgeProblemIdReq('get', this.$route.params.id)
        this.problem = {
          id: res.data.id,
          interval: res.data.interval,
          maxSubmission: res.data['max_submission'],
          name: res.data.name,
          description: res.data.description,
          type: res.data.type.toString(),
          resetTime: moment(res.data['reset_time']).toISOString(true),
          answer: res.data.answer,
          choices: res.data.choices,
          testcases: res.data['test_cases'],
          problemData: res.data['problem_data'],
          problemFilename: res.data['problem_data__filename'],
          explain: res.data.explain
        }
        this.ready = true
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async problemFile() {
      return problemFileReq('get', this.$route.params.id)
    },
    async testcaseFile(id) {
      return testcaseFileReq('get', id)
    },
    testcaseAbstract(info) {
      return testcaseAbstract(info)
    }
  }
}
</script>
