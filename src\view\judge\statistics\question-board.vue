<template>
  <div ref="dom" class="charts chart-plot" />
</template>

<script>
import * as echarts from 'echarts'
import { on, off } from '@/libs/tools'
import tdTheme from '@/assets/chart.json'

echarts.registerTheme('tdTheme', tdTheme)

export default {
  name: 'QuestionBoard',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    text: {
      type: String,
      default: ''
    },
    subtext: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dom: null
    }
  },
  watch: {
    value: {
      handler() {
        this.refresh()
      },
      deep: true
    }
  },
  mounted() {
    this.dom = echarts.init(this.$refs.dom, 'tdTheme')
    if (this.value) {
      this.refresh()
    }
  },
  beforeDestroy() {
    off(window, 'resize', this.resize)
  },
  methods: {
    resize() {
      this.dom.resize()
    },
    refresh() {
      this.dom.clear()
      const options = this.chartOption()
      this.dom.setOption(options)
      on(window, 'resize', this.resize)
    },
    chartOption() {
      if (!this.value.category || !this.value.data) {
        return {
          title: {
            text: this.text,
            subtext: this.subtext,
            left: 'center'
          }
        }
      }

      // 创建数据副本
      const sortedCategory = [...this.value.category]
      const sortedData = [...this.value.data]

      const options = {
        title: {
          text: this.text,
          subtext: this.subtext,
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: sortedData.map((item) => item.name).sort((a, b) => a.localeCompare(b))
        },
        radar: {
          indicator: sortedCategory
            .sort((a, b) => a.localeCompare(b))
            .map((category) => ({
              name: category,
              max: 100
            })),
          center: ['50%', '60%']
        },
        series: [
          {
            type: 'radar',
            data: sortedData
              .sort((a, b) => a.name.localeCompare(b.name))
              .map((item) => ({
                value: item.data,
                name: item.name,
                symbolSize: 4,
                lineStyle: {
                  width: 2
                },
                areaStyle: {
                  opacity: 0.1
                }
              }))
          }
        ]
      }

      return options
    }
  }
}
</script>

<style scoped>
.charts {
  width: 100%;
  height: 100%;
}
</style>
