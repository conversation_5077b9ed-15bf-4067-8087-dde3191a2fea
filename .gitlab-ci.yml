stages:
  - build
  - deploy

build_production:
  stage: build
  only:
    - master
  tags:
    - machine001
  script:
    - docker build --build-arg build_mode=prod -t "trebuchet-frontend:ci_job_$CI_PIPELINE_ID" .

build_develop:
  stage: build
  only:
    - develop
  tags:
    - machine001
  script:
    - docker build --build-arg build_mode=dev -t "trebuchet-frontend:ci_job_$CI_PIPELINE_ID" .

build_merge_requests:
  stage: build
  only:
    refs:
      - merge_requests
  tags:
    - machine001
  script:
    - docker build --build-arg build_mode=dev -t "trebuchet-frontend:ci_job_$CI_PIPELINE_ID" .

deploy_production:
  stage: deploy
  only:
    - master
  tags:
    - machine001
  script:
    - docker stop trebuchet-frontend-production && docker rm trebuchet-frontend-production || true
    - docker run --restart=unless-stopped --name=trebuchet-frontend-production -d -p 8101:8000 "trebuchet-frontend:ci_job_$CI_PIPELINE_ID"

deploy_develop:
  stage: deploy
  only:
    - develop
  tags:
    - machine001
  script:
    - docker stop trebuchet-frontend-develop && docker rm trebuchet-frontend-develop || true
    - docker run --restart=unless-stopped --name=trebuchet-frontend-develop -d -p 9101:8000 "trebuchet-frontend:ci_job_$CI_PIPELINE_ID"
