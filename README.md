# 管理前端

## 组件库相关

- 使用的组件库为[ iView 2.x 版本](http://v2.iviewui.com/)，已停止维护
- 搜索引擎搜索到的多为 iView 3.x 版本，请注意区分

## 构建和运行步骤

- 安装所需依赖：`npm install`
- 使用本地后端运行：`npm run serve-dev-local`
- 使用开发服后端运行：`npm run serve-dev`
- 使用生产服后端运行：`npm run serve-prod`
- 使用开发服后端构建：`npm run build-dev`
- 使用生产服后端构建：`npm run build-prod`

## 相关链接

- 开发服 API：`http://cscore.buaa.edu.cn:9100`
- 开发服前端：`http://cscore.buaa.edu.cn:9101`
- 生产服 API：`http://cscore.buaa.edu.cn:8100`
- 生产服前端：`http://cscore.buaa.edu.cn:8101`

## 其它

- 目前未完成复审的目录：
  ```plain
  /src/view/course
  /src/view/exam-check
  /src/view/filter-table
  /src/view/judge
  /src/view/on-exam
  /src/view/student
  /src/view/class
  /src/view/exam
  /src/view/message
  /src/view/room
  /src/view/templates
  /src/view/user
  ```
