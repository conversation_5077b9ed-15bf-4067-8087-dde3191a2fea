import qs from 'qs'
import { direct } from '@/api/util'

export const loginReq = (params) => {
  const transformer = (params) => qs.stringify(params, { arrayFormat: 'brackets' })
  return direct('/api/token-auth', 'post', { data: params, transformRequest: [transformer] })
}

export const refreshTokenReq = (refreshToken) => {
  const headers = refreshToken ? { Authorization: 'Bearer ' + refreshToken } : {}
  return direct('/api/token-refresh', 'get', { headers })
}
