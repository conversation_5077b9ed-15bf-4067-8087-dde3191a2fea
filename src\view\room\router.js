import Main from '@/view/index/main'

export const roomRouter = {
  path: '/room',
  name: 'room',
  component: Main,
  meta: {
    title: '教室管理',
    icon: 'ios-home',
    jumpRoute: '/room/room-table'
  },
  children: [
    {
      path: 'room-table',
      name: 'room_table',
      meta: {
        title: '教室总览'
      },
      component: () => import('@/view/room/room-table')
    },
    {
      path: 'room-upload',
      name: 'room_upload',
      meta: {
        title: '教室上传'
      },
      component: () => import('@/view/room/room-upload')
    },
    {
      path: 'room-editing/:id',
      name: 'room_editing',
      meta: {
        title: '教室编辑',
        hideInMenu: true
      },
      component: () => import('@/view/room/room-editing')
    }
  ]
}
