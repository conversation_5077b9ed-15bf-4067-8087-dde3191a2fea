<template>
  <Card>
    <Row v-if="noExamRecord" type="flex" justify="space-between">
      <Col :xs="24" :md="10" style="margin: 1em auto">
        <Row type="flex" justify="flex-start">
          <Col :xs="24" :md="24">
            <Card>
              <p slot="title">基本信息</p>
              <table class="record-table">
                <tr>
                  <td><b>学号</b></td>
                  <td>{{ examRecord['student__student_id'] }}</td>
                </tr>
                <tr>
                  <td><b>姓名</b></td>
                  <td>{{ examRecord['student__name'] }}</td>
                </tr>
                <tr>
                  <td><b>Project Name</b></td>
                  <td>{{ examRecord['project_in_exam__project__name'] }}</td>
                </tr>
                <tr>
                  <td><b>考场及座位</b></td>
                  <td>{{ `${studentRoom} - ${studentSeat}` }}</td>
                </tr>
                <tr>
                  <td><b>Exam Id</b></td>
                  <td>{{ examRecord['project_in_exam__exam__id'] }}</td>
                </tr>
                <tr>
                  <td><b>当前状态</b></td>
                  <td>
                    <tag :color="status.color">
                      {{ status.name }}
                    </tag>
                  </td>
                </tr>
                <tr>
                  <td><b>是否通过课上</b></td>
                  <td>
                    <tag :color="passed ? 'green' : 'red'">
                      {{ passed ? '通过' : '未通过' }}
                    </tag>
                  </td>
                </tr>
                <tr>
                  <td><b>检查者</b></td>
                  <td>{{ examRecord['examinant__username'] }}</td>
                </tr>
                <tr>
                  <td><b>签到时间</b></td>
                  <td>{{ checkInTime }}</td>
                </tr>
                <tr>
                  <td><b>签退时间</b></td>
                  <td>{{ checkOutTime }}</td>
                </tr>
              </table>
            </Card>
          </Col>
        </Row>
        <br />
        <Row type="flex">
          <Col :xs="24" :md="24">
            <Card>
              <p slot="title">课上题目通过情况</p>
              <Table :data="problems" :columns="problemColumns" />
            </Card>
          </Col>
        </Row>
        <br />
        <Row type="flex">
          <Col :xs="24" :md="24">
            <Card>
              <p slot="title">修改学生延时</p>
              <Form ref="updateExtendTime" :model="examRecord" :rules="updateRule" :label-width="80">
                <form-item prop="extend_time" label="考试延时">
                  <Input v-model="examRecord.extend_time" />
                </form-item>
                <form-item>
                  <Button :disabled="disable" type="primary" @click="handleSubmit('updateExtendTime')">提交</Button>
                </form-item>
              </Form>
            </Card>
          </Col>
        </Row>
      </Col>
      <Col offset="0" :xs="24" :md="12" style="margin: 1em auto">
        <Card>
          <p slot="title">检查提交</p>
          <Form ref="checkExamRecord" :model="examRecord" :rules="checkRule" :label-width="120">
            <div v-for="[key, value] in Object.entries(questionDict)" :key="key">
              <Divider>题目类型:{{ key }}</Divider>
              <question-item :questionDict="value" :record="question_answer_record" />
            </div>
            <form-item prop="recommend_level" label="评价等级"> 评价等级为{{ examRecord.old_result }} </form-item>
            <form-item prop="check_result" label="设置新评价等级">
              <radio-group v-model="examRecord.check_result">
                <radio label="A" />
                <radio label="B" />
                <radio label="C" />
                <radio label="F" />
              </radio-group>
            </form-item>
            <form-item prop="recommend_level" label="推荐评价等级"> 推荐评价等级为{{ recommendLevel }} </form-item>
            <form-item prop="check_comment" label="备注">
              <Input v-model="examRecord.check_comment" type="textarea" :rows="4" />
            </form-item>
          </Form>
        </Card>
      </Col>
    </Row>
    <br />
    <Row type="flex" justify="end">
      <Col style="margin: 1em">
        <Button type="primary" @click="showSkip">开启连测</Button>
      </Col>
      <Col style="margin: 1em">
        <Button type="primary" @click="handleSubmit('checkExamRecord')">提交</Button>
      </Col>
      <Modal v-model="skipModal" title="请确认该学生是否要连测" @on-ok="onSkip">
        <p style="font-size: 15px; margin-bottom: 12px">因为连测不支持撤销，所以需要输入学号验证</p>
        <Input v-model="studentID" style="width: 300px" />
      </Modal>
    </Row>
    <p style="font-weight: bold; font-size: 16px; margin: 10px">历次检查记录</p>
    <Row>
      <Col>
        <Table :data="oldExamRecord" :columns="columns" />
      </Col>
    </Row>
  </Card>
</template>

<script>
import { examRecordReq, examRecordIdReq, examRecordProblemReq } from '@/api/exam-record'
import { getErrModalOptions, getLocalTime } from '@/libs/util'
import { progressSkip } from '@/api/exam'
import { userProfileReq } from '@/api/user'
import { studentCurrentSeat } from '@/api/on-exam'
import { recordStatus, recordCheckResult } from './check-record-constants'
import questionItem from './question-item.vue'
import { ActionButton } from '@/libs/render-item'

export default {
  name: 'CheckView',
  components: { questionItem },
  data() {
    return {
      carousel: 0,
      questionTab: 0,
      question_ids: [],
      question_answer_record: [],
      questionModal: false,
      questionDict: {},
      currentQuestion: {},
      currentAnswer: {
        grade: 0,
        question_id: null,
        comment: ''
      },
      studentRoom: null,
      studentSeat: null,
      examRecord: {},
      oldExamRecord: [],
      columns: [
        {
          title: 'Exam Record ID',
          key: 'id'
        },
        {
          title: 'Project Name',
          key: 'project_in_exam__project__name'
        },
        {
          title: 'Exam ID',
          key: 'project_in_exam__exam__id'
        },
        {
          title: 'Status',
          key: 'status',
          render: (h, params) => h('Tag', {}, recordStatus[params.row.status].name)
        },
        {
          title: 'Examinant',
          key: 'examinant__username'
        },
        {
          title: 'Check In At',
          key: 'checked_in_at',
          render: (h, params) => h('div', getLocalTime(params.row.checked_in_at))
        },
        {
          title: 'Check Out At',
          key: 'checked_out_at',
          render: (h, params) => h('div', getLocalTime(params.row.checked_out_at))
        },
        {
          title: 'Grade',
          render: (h, params) => {
            return h(
              'p',
              params.row.status === 2
                ? recordCheckResult.find((elm) => {
                    return elm.value === params.row.check_result
                  }).name
                : '暂无评级'
            )
          }
        },
        {
          title: 'Comment',
          render: (h, params) => h('p', params.row.check_comment === '' ? '暂无评语' : params.row.check_comment)
        },
        {
          title: '查看修改记录',
          render: (h, params) => ActionButton(h, () => this.updateComment(params.row.id), '查看详情', false)
        }
      ],
      checkRule: {
        check_result: [{ required: true, message: '请选择一个评级', trigger: 'blur' }]
      },
      updateRule: {
        extend_time: [
          {
            validator(rule, value, callback) {
              const a = parseInt(value)
              if (isNaN(a)) {
                callback(new Error('should input a number'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      noExamRecord: false,
      passed: false,
      problems: [],
      problemColumns: [
        {
          title: '题目',
          key: 'problem_name'
        },
        {
          title: '通过情况',
          key: 'judge_result',
          render: (h, params) => {
            return h('div', [
              h(
                'a',
                {
                  on: {
                    click: () => {
                      if (params.row.record_id === null) {
                        this.$Notice.info({ title: '该学生未作这道题' })
                      } else {
                        this.$router.push({ name: 'judge_detail', params: { id: params.row.record_id } })
                      }
                    }
                  }
                },
                params.row.judge_result
              )
            ])
          }
        }
      ],
      studentID: null,
      skipModal: false,
      oldER: {
        check_result: null,
        check_comment: null
      }
    }
  },
  computed: {
    checkExamUpdate() {
      return {
        check_result: recordCheckResult.find((ele) => {
          return ele.name === this.examRecord.check_result
        }).value,
        check_comment: this.examRecord.check_comment,
        status: 2
      }
    },
    updateExtendTime() {
      return {
        extend_time: parseInt(this.examRecord.extend_time)
      }
    },
    disable() {
      return this.examRecord.status !== 1
    },
    status() {
      return recordStatus[this.examRecord.status]
    },
    checkInTime() {
      return getLocalTime(this.examRecord.checked_in_at)
    },
    checkOutTime() {
      return getLocalTime(this.examRecord.checked_out_at)
    },
    confirm() {
      return this.studentID === this.examRecord['student__student_id']
    },
    recordStatus: () => recordStatus,
    recommendLevel() {
      let cnt = 0
      let total = 0
      this.question_answer_record.forEach((item) => {
        if (item.a == 'F' || item.a == 'D' || item.a == 'C' || item.a == 'B' || item.a == 'A' || item.a == 'A+') {
          total += recordCheckResult.find((elm) => elm.name === item.a).value
          cnt++
        }
      })
      if (cnt === 0) {
        return '还未答题'
      }
      let average = total / cnt
      if (average - Math.floor(average) > 0.5) {
        average = Math.ceil(average)
      } else {
        average = Math.floor(average)
      }
      return recordCheckResult.find((elm) => elm.value === average).name
    }
  },
  watch: {
    recommendLevel: function (val) {
      this.examRecord.check_result = val
    }
  },
  mounted() {
    this.loadData()
    if (this.$store.state.user.userDefaultCourse === -1) {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
          } else {
            this.$Notice.info({ title: '请在联系高阶设置当前课程' })
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    }
  },
  methods: {
    async loadData() {
      try {
        await examRecordIdReq('get', this.$route.params.id, {}).then((res) => {
          this.examRecord = res.data
          this.examRecord.check_result = recordCheckResult.find((each) => each.value === res.data.check_result).name
          this.examRecord.old_result = this.examRecord.check_result
          this.passed = res.data.passed
          this.question_answer_record = res.data.question_answer_record ?? []

          let questions = res.data.question ?? {}
          if (Array.isArray(questions)) {
            this.questionDict = questions.reduce((acc, curArray, index) => {
              acc[index + 1] = {}
              curArray.forEach((item) => {
                acc[index + 1][item] = 0
              })
              return acc
            }, {})
          } else {
            this.questionDict = questions
          }
        })
        await studentCurrentSeat(this.examRecord['student__student_id'], this.examRecord['project_in_exam__exam__id'])
          .then((res) => {
            this.studentSeat = res.data['name']
            this.studentRoom = res.data['room_name']
          })
          .catch((err) => {
            this.$Modal.error(getErrModalOptions(err))
          })
        await examRecordProblemReq('get', this.examRecord.id, {}).then((res) => {
          this.problems = []
          Object.keys(res.data.problems).forEach((key) => {
            this.problems.push({
              problem_name: res.data.problems[key].problem_name,
              judge_result: res.data.problems[key].judge_result,
              record_id: res.data.problems[key].record_id
            })
          })
        })
        await examRecordReq('get', {
          student_id__contains: this.examRecord['student__student_id'],
          page_size: 500,
          order_by: '-project_in_exam__exam__date'
        }).then((res) => {
          if (res.data.models.length === 0) {
            this.noExamRecord = false
            this.$Modal.warning({
              title: '该学生无检查记录'
            })
          } else {
            this.noExamRecord = true
            this.oldExamRecord = res.data.models
          }
        })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    update(data) {
      return examRecordIdReq('put', this.examRecord.id, data)
    },
    async handleSubmit(name) {
      try {
        await new Promise((resolve, reject) =>
          this.$refs[name].validate((valid) => (valid ? resolve() : reject(new Error('提交的数据有误'))))
        )
        if (name === 'checkExamRecord') {
          const invalid =
            (this.passed && this.checkExamUpdate.check_result === -1) ||
            (!this.passed && this.checkExamUpdate.check_result !== -1)
          if (invalid) {
            const selection = await new Promise((resolve) =>
              this.$Modal.confirm({
                title: '检测到评级与该学生课上实际通过情况不一致，是否确认要继续提交',
                onOk: () => resolve(true),
                onCancel: () => resolve(false)
              })
            )
            if (!selection) {
              return
            }
          }
          await this.update({
            question_answer_record: this.question_answer_record,
            ...this.checkExamUpdate
          })
        } else {
          await this.update(this.updateExtendTime)
        }
        this.$Notice.success({ title: '修改成功' })
        await this.loadData()
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    onSkip() {
      if (this.confirm) {
        progressSkip(this.$store.state.user.userDefaultCourse, this.examRecord['student__student_id'])
          .then(() => {
            this.$Notice.success({ title: '连测成功' })
            this.loadData()
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
        this.studentID = null
      } else {
        this.$Notice.warning({ title: '学号有误' })
        this.studentID = null
      }
    },
    showSkip() {
      this.skipModal = true
    },
    updateComment(exam_record_id) {
      this.$router.push({ name: 'check_exam_record', params: { id: exam_record_id } })
      location.reload()
    }
  }
}
</script>

<style lang="less" scoped>
.record-table td {
  font-size: 15px;
  padding-left: 15px;
}
</style>
