import Main from '@/view/index/main'

export const studentRouter = {
  path: '/student',
  name: 'student',
  component: Main,
  meta: {
    title: '学生信息',
    icon: 'ios-people',
    jumpRoute: '/student/student-table'
  },
  children: [
    {
      path: 'student-table',
      name: 'student_table',
      meta: {
        title: '学生总览'
      },
      component: () => import('@/view/student/student-table')
    },
    {
      path: 'student-upload',
      name: 'student_upload',
      meta: {
        title: '上传学生信息'
      },
      component: () => import('@/view/student/student-upload')
    }
  ]
}
