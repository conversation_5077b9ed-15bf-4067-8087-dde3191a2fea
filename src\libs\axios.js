import _ from 'lodash'
import axios from 'axios'
import router from '@/router'
import { refreshTokenReq } from '@/api/auth'
import { getRefreshToken, getToken, setRefreshToken, setToken } from '@/libs/tools'

const reLogin = () => {
  setToken('')
  setRefreshToken('')
  router.push({ name: 'login' }).then(() => {})
}

const baseURL = process.env.VUE_APP_API_URL.replace(/\/$/, '')

class HttpRequest {
  constructor(baseUrl = baseURL) {
    this.baseUrl = baseUrl
    this.queue = {}
  }

  getInsideConfig() {
    return { baseURL: this.baseUrl, headers: {} }
  }

  destroy(url) {
    delete this.queue[url]
  }

  request(options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }

  interceptors(instance, url) {
    instance.interceptors.request.use(
      (config) => {
        config.headers['Authorization'] = config.headers['Authorization'] || 'Bearer ' + (getToken() || 'invalid_token')
        this.queue[url] = true
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )
    instance.interceptors.response.use(
      async (res) => {
        this.destroy(url)
        return Promise.resolve(res)
      },
      async (error) => {
        if (error.response) {
          this.destroy(url)
          if (error.response.status === 401) {
            if (_.includes(error.config.url, 'refresh')) {
              return Promise.reject(error)
            }
            const refreshToken = getRefreshToken()
            if (refreshToken) {
              try {
                const res = await refreshTokenReq(refreshToken)
                const data = res.data
                setToken(data['access_token'])
                setRefreshToken(refreshToken)
                const reqConfig = error.config
                reqConfig.headers['Authorization'] = 'Bearer ' + data['access_token']
                return axios.request(reqConfig)
              } catch (err) {
                err.response.status === 401 && reLogin()
                return Promise.reject(err)
              }
            } else {
              location.href.endsWith('/login') || reLogin()
              return Promise.reject(error)
            }
          }
        }
        return Promise.reject(error)
      }
    )
  }
}

export default HttpRequest
