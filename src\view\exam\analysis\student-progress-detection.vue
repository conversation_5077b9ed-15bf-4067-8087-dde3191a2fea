<template>
  <div>
    <!-- 连续挂科检测区域 -->
    <Card style="margin-bottom: 24px">
      <div slot="title" style="display: flex; align-items: center; font-weight: bold">
        <Icon
          :type="collapsed.fails ? 'ios-arrow-forward' : 'ios-arrow-down'"
          @click="toggleCollapse('fails')"
          style="cursor: pointer; margin-right: 8px; font-size: 16px"
        />
        <Icon type="ios-alert" style="color: #ed4014; margin-right: 8px" />
        连续课上考试不通过检测
      </div>

      <div v-show="!collapsed.fails">
        <Row style="margin-bottom: 16px">
          <Col span="8">
            <div style="display: flex; align-items: center">
              <span style="margin-right: 12px; white-space: nowrap">连续不通过次数阈值：</span>
              <InputNumber v-model="failsThreshold" :min="2" :max="10" style="width: 120px" />
            </div>
          </Col>
          <Col span="16">
            <div style="text-align: right">
              <Button
                type="primary"
                @click="loadConsecutiveFailsStudents"
                :loading="failsLoading"
                style="margin-right: 8px"
              >
                <Icon type="ios-refresh" />
                刷新数据
              </Button>
              <Button type="success" @click="exportFailsCSV" :disabled="filteredFailsStudents.length === 0">
                <Icon type="ios-download" />
                导出 CSV
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          :data="filteredFailsStudents"
          :columns="failsColumns"
          :loading="failsLoading"
          border
          stripe
          size="default"
        >
          <template slot-scope="{ row }" slot="consecutiveFailsSlot">
            {{ row.consecutive_fails }}
          </template>
        </Table>

        <div v-if="filteredFailsStudents.length === 0 && !failsLoading" class="empty-state">
          <Icon type="ios-information-circle" size="48" color="#c5c8ce" />
          <p>暂无连续不通过的学生</p>
        </div>
      </div>
    </Card>

    <!-- 连续无上机资格检测区域 -->
    <Card>
      <div slot="title" style="display: flex; align-items: center; font-weight: bold">
        <Icon
          :type="collapsed.noQualification ? 'ios-arrow-forward' : 'ios-arrow-down'"
          @click="toggleCollapse('noQualification')"
          style="cursor: pointer; margin-right: 8px; font-size: 16px"
        />
        <Icon type="ios-close-circle" style="color: #ff9900; margin-right: 8px" />
        连续多次没有课上资格检测
      </div>

      <div v-show="!collapsed.noQualification">
        <Row style="margin-bottom: 16px">
          <Col span="8">
            <div style="display: flex; align-items: center">
              <span style="margin-right: 12px; white-space: nowrap">连续无资格次数阈值：</span>
              <InputNumber v-model="noQualificationThreshold" :min="2" :max="10" style="width: 120px" />
            </div>
          </Col>
          <Col span="16">
            <div style="text-align: right">
              <Button
                type="primary"
                @click="loadConsecutiveNoQualificationStudents"
                :loading="noQualificationLoading"
                style="margin-right: 8px"
              >
                <Icon type="ios-refresh" />
                刷新数据
              </Button>
              <Button
                type="success"
                @click="exportNoQualificationCSV"
                :disabled="filteredNoQualificationStudents.length === 0"
              >
                <Icon type="ios-download" />
                导出 CSV
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          :data="filteredNoQualificationStudents"
          :columns="noQualificationColumns"
          :loading="noQualificationLoading"
          border
          stripe
          size="default"
        >
          <template slot-scope="{ row }" slot="consecutiveNoQualificationSlot">
            {{ row.consecutive_no_qualification }}
          </template>
        </Table>

        <div v-if="filteredNoQualificationStudents.length === 0 && !noQualificationLoading" class="empty-state">
          <Icon type="ios-information-circle" size="48" color="#c5c8ce" />
          <p>暂无连续无资格的学生</p>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import { getConsecutiveFailsStudents, getConsecutiveNoQualificationStudents } from '@/api/exam'
import { userProfileReq } from '@/api/user'

export default {
  name: 'StudentProgressDetection',
  data() {
    return {
      curCourse: null,
      // 折叠状态控制
      collapsed: {
        fails: false, // 连续挂科检测
        noQualification: false // 连续无上机资格检测
      },
      // 连续挂科相关数据
      failsThreshold: 2,
      failsStudents: [],
      failsLoading: false,
      failsColumns: [
        {
          title: '学号',
          key: 'student_id',
          sortable: true,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'student_name',
          align: 'center'
        },
        {
          title: '院系',
          key: 'department',
          align: 'center'
        },
        {
          title: '任课教师',
          key: 'instructor_class',
          align: 'center'
        },
        {
          title: '连续不通过次数',
          key: 'consecutive_fails',
          sortable: true,
          align: 'center',
          slot: 'consecutiveFailsSlot'
        }
      ],

      // 连续无上机资格相关数据
      noQualificationThreshold: 2,
      noQualificationStudents: [],
      noQualificationLoading: false,
      noQualificationColumns: [
        {
          title: '学号',
          key: 'student_id',
          sortable: true,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'student_name',
          align: 'center'
        },
        {
          title: '院系',
          key: 'department',
          align: 'center'
        },
        {
          title: '任课教师',
          key: 'instructor_class',
          align: 'center'
        },
        {
          title: '连续无资格次数',
          key: 'consecutive_no_qualification',
          sortable: true,
          align: 'center',
          slot: 'consecutiveNoQualificationSlot'
        }
      ]
    }
  },
  computed: {
    filteredFailsStudents() {
      return this.failsStudents.filter((student) => student.consecutive_fails >= this.failsThreshold)
    },
    filteredNoQualificationStudents() {
      return this.noQualificationStudents.filter(
        (student) => student.consecutive_no_qualification >= this.noQualificationThreshold
      )
    }
  },
  mounted() {
    this.loadCurCourse()
  },
  methods: {
    toggleCollapse(section) {
      this.collapsed[section] = !this.collapsed[section]
    },
    loadCurCourse() {
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在课程信息/课程总览选择当前课程'
            })
          } else {
            this.$store.commit('user/setUserDefaultCourse', res.data.course.id)
            this.curCourse = res.data.course.id
            this.loadConsecutiveFailsStudents()
            this.loadConsecutiveNoQualificationStudents()
          }
        })
        .catch(() => {
          this.$Message.error('获取用户信息失败')
        })
    },
    async loadConsecutiveFailsStudents() {
      if (!this.curCourse) {
        this.$Message.error('未获取到课程信息')
        return
      }

      this.failsLoading = true
      try {
        const response = await getConsecutiveFailsStudents(this.curCourse)
        if (response.status === 200 && response.data) {
          this.failsStudents = response.data.students || []
        } else {
          this.$Message.error('查询失败')
        }
      } catch (error) {
        console.error('查询连续挂科学生失败:', error)
        const errorMessage = error.response?.data?.message || error.message || '查询失败，请检查网络连接'
        this.$Message.error(errorMessage)
      } finally {
        this.failsLoading = false
      }
    },

    async loadConsecutiveNoQualificationStudents() {
      if (!this.curCourse) {
        this.$Message.error('未获取到课程信息')
        return
      }

      this.noQualificationLoading = true
      try {
        const response = await getConsecutiveNoQualificationStudents(this.curCourse)
        if (response.status === 200 && response.data) {
          this.noQualificationStudents = response.data.students || []
        } else {
          this.$Message.error('查询失败')
        }
      } catch (error) {
        console.error('查询连续无上机资格学生失败:', error)
        const errorMessage = error.response?.data?.message || error.message || '查询失败，请检查网络连接'
        this.$Message.error(errorMessage)
      } finally {
        this.noQualificationLoading = false
      }
    },

    exportFailsCSV() {
      this.exportCSV(
        this.filteredFailsStudents,
        this.failsColumns,
        `连续不通过学生名单_阈值${this.failsThreshold}次_${new Date().toISOString().split('T')[0]}.csv`
      )
    },

    exportNoQualificationCSV() {
      this.exportCSV(
        this.filteredNoQualificationStudents,
        this.noQualificationColumns,
        `连续无课上资格学生名单_阈值${this.noQualificationThreshold}次_${new Date().toISOString().split('T')[0]}.csv`
      )
    },

    exportCSV(data, columns, filename) {
      if (data.length === 0) {
        this.$Message.warning('没有数据可导出')
        return
      }

      try {
        const csvData = this.convertToCSV(data, columns)
        this.downloadCSV(csvData, filename)
        this.$Message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$Message.error('导出失败')
      }
    },

    convertToCSV(data, columns) {
      const headers = columns.map((col) => col.title).join(',')
      const rows = data.map((row) =>
        columns
          .map((col) => {
            const value = row[col.key] || ''
            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
          })
          .join(',')
      )
      return '\ufeff' + [headers, ...rows].join('\n')
    },

    downloadCSV(csvData, filename) {
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style scoped>
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #c5c8ce;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
  color: #999;
}

::v-deep .ivu-btn {
  margin-right: 8px;
}

::v-deep .ivu-btn:last-child {
  margin-right: 0;
}

::v-deep .ivu-card-head .ivu-icon {
  margin-right: 8px;
}
</style>
