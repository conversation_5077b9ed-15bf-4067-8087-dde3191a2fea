<template>
  <Card dis-hover>
    <filter-table
      :data="tableData"
      :columns="columns"
      :default-filter="
        this.$store.state.app.tableFilter.pushMsgTable ? this.$store.state.app.tableFilter.pushMsgTable : {}
      "
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="float: left">
        <Button type="primary" @click="create">创建推送消息</Button>
      </div>
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-total
          show-elevator
          @on-change="changePage"
        />
      </div>
    </div>
  </Card>
</template>

<script>
import { pushMsgReq } from '@/api/push-message'
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions, getLocalTime } from '@/libs/util'
import _ from 'lodash'
import { LinkButton, Spacer, TagByObj } from '@/libs/render-item'

const pushStatus = {
  0: {
    value: 0,
    name: '未推送',
    color: 'yellow'
  },
  1: {
    value: 1,
    name: '推送成功',
    color: 'green'
  },
  2: {
    value: 2,
    name: '部分推送失败',
    color: 'red'
  }
}

export default {
  name: 'PushMsgTable',
  components: { FilterTable },
  data() {
    return {
      tableData: [],
      columns: [
        {
          title: 'ID',
          key: 'id'
        },
        {
          title: 'Title',
          key: 'title',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Content',
          key: 'content',
          filter: {
            type: 'Input'
          },
          render: (h, params) => h('div', this.truncateText(params.row.content, 20))
        },
        {
          title: 'Comment',
          key: 'comment',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Push Status',
          key: 'status',
          render: (h, params) => TagByObj(h, pushStatus[params.row.status]),
          filter: {
            type: 'Select',
            option: pushStatus
          }
        },
        {
          title: 'Created By',
          key: 'created_by',
          render: (h, params) => h('div', params.row['created_by__username'] || '匿名')
        },
        {
          title: 'Created At',
          key: 'created_at',
          render: (h, params) => h('div', getLocalTime(params.row['created_at']))
        },
        {
          title: 'Action',
          key: 'action',
          align: 'center',
          render: (h, params) =>
            h('div', [
              LinkButton(h, params.row.id, 'push_message_show', '详情', false),
              Spacer(h),
              LinkButton(h, params.row.id, 'push_message_detail', '编辑', params.row.status !== pushStatus[0].value)
            ])
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-created_at',
      filter: {}
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.pushMsgTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.pushMsgTable)
    }
    this.curPage = this.$store.state.app.tablePage.pushMsgTable ? this.$store.state.app.tablePage.pushMsgTable : 1
    this.loadData(1)
  },
  methods: {
    async getData(index) {
      const res = await pushMsgReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
      this.tableData = res.data['models']
      this.totalCnt = res.data['total_count']
      this.curPage = res.data['page_now']
      return res
    },
    async loadData(index) {
      try {
        const res = await this.getData(index)
        this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'pushMsgTable' })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async changePage(index) {
      try {
        const res = await this.getData(index)
        this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'pushMsgTable' })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    async onSearch(search) {
      search = this.refactorSearchObject(search)
      try {
        const res = await this.getData(1)
        this.$store.commit('setTableFilter', { filter: search, name: 'pushMsgTable' })
        this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'pushMsgTable' })
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {}
      Object.keys(search).forEach((key) => {
        this.filter[key + '__contains'] = search[key]
      })
      return searchNew
    },
    create() {
      this.$router.push({ name: 'push_message_create' })
    },
    truncateText(str, max) {
      return str.length > max ? str.substring(0, max) + '...' : str
    }
  }
}
</script>
