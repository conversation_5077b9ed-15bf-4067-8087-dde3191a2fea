import { getRequest } from '@/api/util'

export const login = ({ userName, password }) => {
  return getRequest('/login', 'post', { userName, password })
}

export const userProfileReq = (method, params) => {
  return getRequest(`/api/user-profile`, method, params)
}

export const userIdProfileReq = (method, id, params) => {
  return getRequest(`/api/user-profile/${id}`, method, params)
}

export const authorizedCoursesReq = (method, userId, params) => {
  return getRequest(`/api/user-profile/${userId}/authorized-courses`, method, params)
}

export const selfAuthorizedCoursesReq = (method, params) => {
  return getRequest(`/api/user-profile/authorized-courses`, method, params)
}

export const logout = () => {
  return getRequest('/logout', 'post')
}

export const createUser = (params) => {
  return getRequest(`/api/user/create`, 'post', params)
}

export const updatePassword = (params) => {
  return getRequest(`/api/user/change-password`, 'post', params)
}

export const deleteUser = (params) => {
  return getRequest(`/api/user/delete`, 'delete', params)
}

export const userListReq = (params) => {
  return getRequest(`/api/user`, 'get', params)
}

export const userIdReq = (method, userId, params) => {
  return getRequest(`/api/user/${userId}`, method, params)
}

export const getUserRole = () => {
  return getRequest(`/api/user/permission`, 'get', {})
}

export const createUserGroup = (params) => {
  return getRequest(`/api/user-group/create`, 'post', params)
}

export const deleteUserGroup = (params) => {
  return getRequest(`/api/user-group/delete`, 'delete', params)
}

export const userGroupListReq = (params) => {
  return getRequest(`/api/user-group`, 'get', params)
}

export const userGroupIdReq = (method, groupId, params) => {
  return getRequest(`/api/user-group/${groupId}`, method, params)
}

export const usersInGroupReq = (groupId, params) => {
  return getRequest(`/api/user-group/${groupId}/users`, 'get', params)
}
