<template>
  <div>
    <Select v-model="selectExam" style="width: 200px" @on-change="onExamChange">
      <Option v-for="item in examList" :key="item.id" :value="item.id">{{ item.id }} : {{ item.date }}</Option>
    </Select>
    <Modal v-model="transferModal" title="变更座位" @on-ok="onTransferStudent">
      <div>将目标学生变更到座位:</div>
      <Input v-model="transferStudentId" placeholder="目标学生的学号" />
    </Modal>
    <card>
      <Collapse :value="initActivePanels">
        <Panel name="1">
          选择教室 {{ `已选择: ` + curRoom.name }}
          <div slot="content">
            <Row>
              <Col v-for="room in roomOptions" :key="room.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="4" :xxl="3">
                <div style="margin: 5px">
                  <Button :type="room.color" style="width: 100%; height: 80px" @click="selectRoom(room.id)">
                    {{ room.name }}
                  </Button>
                </div>
              </Col>
            </Row>
          </div>
        </Panel>
      </Collapse>
      <div ref="alert" v-if="pendingStudents.length > 0" @click="gotoCheckRecord(pendingStudents[0]['exam_record_id'])">
        <Alert banner type="info" style="margin: 1em auto">
          还有{{ pendingStudents.length }}个由你取出队列的学生尚未完成问答，点击继续问答。
        </Alert>
      </div>
      <Tabs :value="$store.state.onClass.overviewActive.name" @input="onTabUpdate">
        <TabPane label="队列" name="queue">
          <Row type="flex" align="middle" justify="space-between">
            <Col flex="auto">
              <Button type="primary" @click="popQueueHeadCheck">取出头部</Button>
            </Col>
            <Col flex="auto">
              <p>队列人数: {{ queueTableData.length }}</p>
            </Col>
          </Row>
          <Table v-if="selected" :data="queueTableData" :columns="queueTableColumns" style="margin: 10px 0" />
          <div v-if="!selected">请选择考场</div>
        </TabPane>
        <TabPane label="签到" name="sign">
          <Radio-group
            v-if="selectExam === activeExam"
            :value="$store.state.onClass.overviewActive.radio"
            @input="onRadioUpdate"
            size="large"
            style="float: left; padding-bottom: 10px"
          >
            <Radio label="仅查看" />
            <Radio label="签到" />
            <Radio label="签退" />
            <Radio label="举手" />
            <Radio label="撤销签到" />
            <Radio label="撤销签退" />
          </Radio-group>
          <Button style="float: right" type="warning" size="small" @click="clearRoomCheck = true">一键清场</Button>
          <launchpad :value="seats" :font-factor="0.7" :limit-height="false" @on-click="onCheckInLaunchpadClick" />
        </TabPane>
        <TabPane label="考场考试记录" name="record">
          <Col flex="auto">
            <Button type="primary" @click="onRoomDuration">考场延时</Button>
          </Col>
          <Modal v-model="roomDuration" title="考场延时" @on-ok="onDurationModify">
            <Form
              ref="durationModifyForm"
              :model="durationOption"
              :label-width="130"
              label-position="right"
              :rules="durationRule"
            >
              <form-item label="选择延时方式" prop="option">
                <radio-group v-model="durationOption.option">
                  <radio label="project">通过 Project 批量延时</radio>
                  <radio label="all">整个考场批量延时</radio>
                </radio-group>
              </form-item>
              <form-item v-if="durationOption.option === 'project'" label="选择一个项目" prop="projectChosen">
                <Select v-model="durationOption.projectChosen">
                  <Option v-for="item in projectSets" :key="item.id" :value="item">Project : {{ item.name }}</Option>
                </Select>
              </form-item>
              <form-item label="输入增加时间 (min)" prop="durationValue">
                <Input v-model="durationOption.durationValue" placeholder="输入想增加的考试时间 (min)" />
              </form-item>
            </Form>
            <template slot="footer">
              <Button type="text" @click="roomDuration = false">取消</Button>
              <Button type="primary" @click="onDurationModify">确认</Button>
            </template>
          </Modal>
          <Row v-if="mini">
            <Col v-for="(record, i) in roomER" :key="i">
              <Card style="margin: 1.5em">
                <Tag slot="title" :color="recordStatus[record.status].color">
                  {{ recordStatus[record.status].name }}
                </Tag>
                <Button slot="extra" type="text" @click="checkCheckRecord(record)">查看检查记录</Button>
                <List>
                  <ListItem v-for="(k, ind) in erColumnsMini" :key="ind">
                    <Row type="flex" justify="space-between" style="width: 100%">
                      <Col flex="auto">
                        {{ k.title }}
                      </Col>
                      <Col flex="auto">
                        {{ record[k.key] }}
                      </Col>
                    </Row>
                  </ListItem>
                </List>
              </Card>
            </Col>
          </Row>
          <Table v-else :data="roomER" :columns="erColumns" style="margin: 10px 0" />
        </TabPane>
        <TabPane label="考场已取出队列" name="taken">
          <Table :data="takeOutData" :columns="takeOutColumns" />
        </TabPane>
      </Tabs>
    </card>
    <Modal v-model="clearRoomCheck" @on-ok="clearRoom">
      <div slot="header" class="ivu-modal-confirm-head">
        <div class="ivu-modal-confirm-head-icon ivu-modal-confirm-head-icon-confirm">
          <Icon class="ivu-icon ivu-icon-ios-help-circle" />
          <span class="ivu-modal-confirm-head-title">确认全部清场</span>
        </div>
      </div>
      <div>你确定要全部清场吗</div>
    </Modal>
  </div>
</template>

<script>
import { getErrModalOptions } from '@/libs/util'
import {
  checkInReq,
  queuePopReq,
  queueReq,
  roomArrangementReq,
  studentSeatReq,
  checkOutReq,
  getRoomErReq,
  undoCheckOutReq,
  undoCheckInReq,
  undoHandsUp,
  handsUpReq,
  clearRoomReq
} from '@/api/on-exam'
import _ from 'lodash'
import { roomReq, roomReqWithId } from '@/api/room'
import { userProfileReq } from '@/api/user'
import Launchpad from '@/components/launchpad'
import { testHandUpReq } from '@/api/test'
import { recordStatus } from '@/view/exam-check/check-record-constants'
import { examReq } from '@/api/exam'
import moment from 'moment'
import { examDurationModify } from '@/api/exam-record'
import { WhitePre } from '@/libs/render-item'
import { Tag } from '@/libs/render-item'

export default {
  name: 'Overview',
  components: { Launchpad },
  data() {
    return {
      recordStatus: recordStatus,
      transferModal: false,
      transferStudentId: '',
      selectedSeat: {},
      selectedStudent: {},
      checkInLaunchpadValue: {
        col: 10,
        row: 20,
        data: _.times(200, _.constant({ text: '1', type: 'default' }))
      },
      curRoom: {},
      curRoomArrangement: {},
      curCourse: null,
      classrooms: [],
      queueTableData: [],
      queueTableColumns: [
        {
          title: '学号',
          key: 'studentid',
          render: (h, params) => WhitePre(h, params.row['studentid'])
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row['student_name'])
        },
        { title: '考试项目', key: 'project_name' },
        { title: '座位', key: 'seat' },
        { title: '教师', key: 'teacher_name' },
        {
          title: '操作',
          render: (h, params) => {
            return [
              h(
                'Button',
                {
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: '确认取出',
                        onOk: () => {
                          this.popStudent(params.row)
                        }
                      })
                    }
                  },
                  props: {
                    type: 'primary'
                  }
                },
                '取出队列'
              ),
              h(
                'Button',
                {
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        title: '确认撤销举手',
                        onOk: () => {
                          this.onUndoHandUp(params.row['studentid'], params.row.project_in_exam)
                        }
                      })
                    }
                  },
                  props: {
                    type: 'error'
                  }
                },
                '撤销举手'
              )
            ]
          }
        }
      ],
      initActivePanels: [2],
      queueTimer: null,
      roomER: [],
      erColumns: [
        {
          title: '学号',
          key: 'student__student_id',
          render: (h, params) => WhitePre(h, params.row['student__student_id'])
        },
        {
          title: '姓名',
          key: 'student__name',
          render: (h, params) => WhitePre(h, params.row['student__name'])
        },
        {
          title: '考试项目',
          key: 'project_in_exam__project__name'
        },
        {
          title: '当前状态',
          key: 'status',
          render: (h, params) => Tag(h, params.row.status_color, params.row.status_name)
        },
        {
          title: '检查人',
          key: 'examinant__username'
        },
        {
          title: '签到于',
          key: 'checked_in_at'
        },
        {
          title: '签退于',
          key: 'checked_out_at'
        },
        {
          title: ' ',
          render: (h, params) => {
            return h(
              'Button',
              {
                props: {
                  type: 'primary'
                },
                on: {
                  click: () => {
                    if (params.row.status === 0) {
                      this.$Notice.info({ title: '该学生未签退，无成绩' })
                    } else {
                      this.$router.push({ name: 'check_exam_record', params: { id: params.row.id } })
                    }
                  }
                }
              },
              '检查记录'
            )
          }
        }
      ],
      erColumnsMini: [
        { title: '学号', key: 'student__student_id' },
        { title: '姓名', key: 'student__name' },
        { title: '考试项目', key: 'project_in_exam__project__name' },
        { title: '检查人', key: 'examinant__username' },
        { title: '签到于', key: 'checked_in_at' },
        { title: '签退于', key: 'checked_out_at' }
      ],
      takeOutData: [],
      takeOutColumns: [
        {
          title: '学号',
          key: 'studentid',
          render: (h, params) => WhitePre(h, params.row['studentid'])
        },
        {
          title: '姓名',
          key: 'student_name',
          render: (h, params) => WhitePre(h, params.row['student_name'])
        },
        {
          title: '座位',
          key: 'seat'
        },
        {
          title: '教师',
          key: 'teacher_name'
        },
        {
          title: '取出者',
          key: 'examinant__username'
        },
        {
          title: '撤销举手',
          render: (h, params) => {
            return h(
              'Button',
              {
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: '确认撤销举手',
                      onOk: () => {
                        this.onUndoHandUp(params.row['studentid'], params.row.project_in_exam)
                      }
                    })
                  }
                },
                props: {
                  type: 'primary'
                }
              },
              '撤销举手'
            )
          }
        }
      ],
      undo: false,
      testHandUpStudentInput: '',
      testHandUpExamInput: '',
      isDevEnv: process.env.NODE_ENV !== 'build-prod',
      examList: [],
      selectExam: null,
      activeExam: null,
      roomDuration: false,
      durationOption: {
        projectChosen: null,
        durationValue: null,
        option: null
      },
      durationRule: {
        option: [{ required: true, trigger: 'blur', message: '请选择延时类型' }],
        projectChosen: [{ trigger: 'blur', validator: this.projectChosenValidate }],
        durationValue: [{ trigger: 'blur', validator: this.durationValueValidate }]
      },
      clearRoomCheck: false
    }
  },
  computed: {
    curRoomId() {
      return this.$store.getters['onClass/currentClassroom']
    },
    projectSets() {
      return _.uniqBy(
        _.map(this.curRoomArrangement, (u) => ({
          name: u.project_name,
          id: u.project_id
        })),
        'id'
      )
    },
    roomOptions() {
      return this.classrooms.map((item) => {
        item.color = this.curRoomId === item.id ? 'success' : 'primary'
        return item
      })
    },
    selected() {
      return this.curRoomId !== null
    },
    passedHash() {
      let hash = {}
      this.roomER.forEach((i) => {
        hash[i['student__student_id']] = i.check_result === -1 ? undefined : true
      })
      return hash
    },
    seats() {
      const seats = this.curRoom.seats || []
      const xMax = _.max(_.map(seats, (item) => item.pos_x))
      const yMax = _.max(_.map(seats, (item) => item.pos_y))
      const data = _.times(xMax * yMax, () => ({ text: '', type: 'default', load: null, isSeat: false }))
      _.forEach(seats, ({ pos_x, pos_y, name, id }) => {
        data[(pos_x - 1) * yMax + pos_y - 1] = { id, text: name, type: 'primary', load: null, isSeat: true }
      })

      _.forEach(this.curRoomArrangement, (item) => {
        const studentId = item['student__student_id']
        const name = item['student__name']
        const pos_x = item['seat__pos_x']
        const pos_y = item['seat__pos_y']
        const seat = data[(pos_x - 1) * yMax + pos_y - 1]
        const project_name = item['project_name']
        let status = item.status === 2 && this.passedHash[item['student__student_id']] ? 4 : item.status
        const seatName = seat.text
        data[(pos_x - 1) * yMax + pos_y - 1] = {
          id: seat.id,
          name: seatName,
          text: seatName,
          subtitle: `${studentId}<br>${name}<br>${project_name}${recordStatus[status].name}`,
          type: recordStatus[status].buttonType,
          load: { studentId, name },
          isSeat: true
        }
      })
      return { row: xMax, col: yMax, data }
    },
    lock() {
      return this.$store.state.onClass.overviewActive.radio === '仅查看'
    },
    mini() {
      return this.$store.getters['view/mini']
    },
    pendingStudents() {
      return this.takeOutData.filter(
        (item) => item.checked === false && item['examinant__username'] === this.$store.getters['user/userName']
      )
    }
  },
  watch: {
    curRoomId() {
      if (this.selected) {
        this.loadQueue()
        this.loadEr()
      }
    }
  },
  beforeDestroy() {
    if (this.queueTimer) {
      clearInterval(this.queueTimer)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    projectChosenValidate(rule, value, callback) {
      if ((!value || value === '') && this.durationOption.option === 'project') {
        callback(new Error('请选择 Project'))
      } else {
        callback()
      }
    },
    durationValueValidate(rule, value, callback) {
      if (!value || value === '') {
        callback(new Error('请输入延时时间'))
      } else if (!Number.isInteger(+value)) {
        callback(new Error('请输入合法的延时时间'))
      } else {
        callback()
      }
    },
    onRoomDuration() {
      this.roomDuration = true
    },
    clearRoom() {
      clearRoomReq('PUT', this.selectExam, this.curRoomId)
        .then((res) => {
          this.changeStatus(res.data['success_check_out'], 2)
          this.changeStatus(res.data['success_raise_hand'], 3)
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changeStatus(lis, value) {
      lis.forEach((item) => {
        const aStudent = this.curRoomArrangement.find((i) => i['student__student_id'] === item)
        aStudent.status = value
      })
    },
    onExamChange() {
      this.selectRoom(this.curRoomId === null ? this.classrooms[0].id : this.curRoomId)
      this.loadEr()
      if (this.selectExam === this.activeExam) {
        this.loadQueue()
        this.queueTimer = setInterval(this.loadQueue, 10000)
      } else if (this.queueTimer) {
        clearInterval(this.queueTimer)
      }
    },
    onCheckInLaunchpadClick(data) {
      const { index } = data
      const clicked = this.seats.data[index]
      if (this.lock) {
        if (clicked.load !== null) {
          this.$copyText(clicked.load.studentId)
            .then(() => {
              this.$Notice.success({ title: '复制成功' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        }
      } else {
        if (clicked.isSeat) {
          this.selectedSeat = clicked
          if (clicked.load === null) {
            this.transferModal = true
          } else {
            this.selectedStudent = clicked.load
            switch (this.$store.state.onClass.overviewActive.radio) {
              case '签到': {
                if (clicked.type !== 'launchpad-button-notCheckIn') {
                  this.$Notice.info({ title: '该学生不处于可签到状态' })
                } else {
                  checkInReq(this.selectedStudent.studentId)
                    .then(() => {
                      this.$Notice.success({ title: '签到成功' })
                      const aStudent = this.curRoomArrangement.find(
                        (item) => item['student__student_id'] === this.selectedStudent.studentId
                      )
                      aStudent.status += 1
                      this.reloadCurRoom(this.curRoomId)
                    })
                    .catch((error) => {
                      this.$Modal.error(getErrModalOptions(error))
                    })
                }
                break
              }
              case '签退': {
                if (clicked.type !== 'warning') {
                  this.$Notice.info({ title: '该学生不处于可签退状态' })
                } else {
                  checkOutReq('put', this.selectedStudent.studentId, {})
                    .then(() => {
                      this.$Notice.success({ title: '签退成功' })
                      const aStudent = this.curRoomArrangement.find(
                        (item) => item['student__student_id'] === this.selectedStudent.studentId
                      )
                      aStudent.status += 1
                    })
                    .catch((error) => {
                      if (error.response.data['detailed_error_code'] === 40301) {
                        this.$Modal.confirm({
                          title: '该学生已满足本次考试的通过情况',
                          content: '是否确定签退',
                          onOk: () => {
                            checkOutReq('put', this.selectedStudent.studentId, {
                              force: true
                            })
                              .then(() => {
                                this.$Notice.success({ title: '签退成功' })
                                const aStudent = this.curRoomArrangement.find(
                                  (item) => item['student__student_id'] === this.selectedStudent.studentId
                                )
                                aStudent.status += 1
                              })
                              .catch((error) => {
                                this.$Modal.error(getErrModalOptions(error))
                              })
                          },
                          onCancel: () => {}
                        })
                      } else {
                        this.$Modal.error(getErrModalOptions(error))
                      }
                    })
                }
                break
              }
              case '举手': {
                const aStudent = this.curRoomArrangement.find(
                  (item) => item['student__student_id'] === this.selectedStudent.studentId
                )
                handsUpReq(this.selectedStudent.studentId, aStudent['project_in_exam_id'])
                  .then(() => {
                    this.$Notice.success({ title: '举手成功' })
                    aStudent.status = 3
                  })
                  .catch((error) => {
                    this.$Modal.error(getErrModalOptions(error))
                  })
                break
              }
              case '撤销签到': {
                if (clicked.type !== 'warning') {
                  this.$Notice.info({ title: '该学生不是正在考试状态，不可撤销签到' })
                } else {
                  undoCheckInReq(this.selectedStudent.studentId)
                    .then(() => {
                      this.$Notice.success({ title: '撤销签到成功' })
                      const aStudent = this.curRoomArrangement.find(
                        (item) => item['student__student_id'] === this.selectedStudent.studentId
                      )
                      aStudent.status -= 1
                    })
                    .catch((error) => {
                      this.$Modal.error(getErrModalOptions(error))
                    })
                }
                break
              }
              case '撤销签退': {
                if (clicked.type !== 'error') {
                  this.$Notice.info({ title: '该学生不是统一签退状态，不可撤销' })
                } else {
                  undoCheckOutReq(this.selectedStudent.studentId)
                    .then(() => {
                      this.$Notice.success({ title: '撤销签退成功' })
                      const aStudent = this.curRoomArrangement.find(
                        (item) => item['student__student_id'] === this.selectedStudent.studentId
                      )
                      aStudent.status -= 1
                    })
                    .catch((error) => {
                      this.$Modal.error(getErrModalOptions(error))
                    })
                }
                break
              }
              default: {
                this.$Modal.info({
                  title: '该学生无可进行操作'
                })
                break
              }
            }
          }
        }
      }
    },
    onTransferStudent() {
      const params = { seat: this.selectedSeat.id }
      studentSeatReq(this.transferStudentId, 'put', params)
        .then(() => {
          this.$Notice.success({ title: '座位更新成功' })
          this.reloadCurRoom(this.curRoomId)
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    reloadCurRoom(roomId) {
      roomReqWithId('get', roomId)
        .then((res) => {
          this.curRoomArrangement = []
          this.curRoom = res.data
          this.$store.commit('onClass/setCurrentClassroom', this.curRoom.id)
          roomArrangementReq(this.curRoomId, 'get', { exam__exact: this.selectExam })
            .then((res) => {
              this.curRoomArrangement = res.data['arrangements']
            })
            .catch((err) => {
              this.$Modal.error(getErrModalOptions(err))
            })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    onDurationModify() {
      const vm = this
      this.$refs.durationModifyForm.validate((valid) => {
        if (valid) {
          const title =
            this.durationOption.option !== 'project'
              ? '将延时整个考场的学生'
              : '将延时 ' + this.durationOption.projectChosen.name + ' 的学生'
          vm.$Modal.confirm({
            title: title,
            content: '是否确定延时' + this.durationOption.durationValue + 'min',
            onOk: () => {
              vm.roomDuration = false
              vm.durationModify()
            },
            onCancel: () => {}
          })
        }
      })
    },
    durationModify() {
      const params = {
        room_id: this.curRoomId,
        minute: parseInt(this.durationOption.durationValue),
        students: [],
        project_id: null
      }
      if (this.durationOption.option === 'project') {
        params.project_id = this.durationOption.projectChosen.id
        this.curRoomArrangement.forEach((item) => {
          if (item.project_id === params.project_id) {
            params.students.push(item['student__student_id'])
          }
        })
      } else {
        this.curRoomArrangement.forEach((item) => {
          params.students.push(item['student__student_id'])
        })
      }
      examDurationModify('put', params)
        .then(() => {
          this.$Notice.success({ title: '修改成功' })
          this.durationOption = {
            projectChosen: null,
            durationValue: null,
            option: null
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    selectRoom(roomId) {
      this.reloadCurRoom(roomId)
    },
    popStudent(student) {
      queuePopReq(this.curRoomId, student['studentid'])
        .then(() => {
          this.gotoCheckRecord(student['exam_record_id'])
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    popQueueHead() {
      queuePopReq(this.curRoomId, null)
        .then((res) => {
          this.gotoCheckRecord(res.data['exam_record_id'])
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    },
    popQueueHeadCheck() {
      if (this.pendingStudents.length !== 0) {
        this.$Modal.confirm({
          title: '还有同学没有问答',
          content: '点击确认继续问答',
          onOk: () => this.$refs['alert'].click(),
          onCancel: () => this.popQueueHead()
        })
      } else {
        this.popQueueHead()
      }
    },
    async loadExams() {
      try {
        let result = await examReq('get', { order_by: '-date' })
        this.examList = result.data.exams
        this.activeExam = this.examList.find((item) => item.active).id
        if (this.activeExam === undefined) {
          this.selectExam = this.examList[0]
          return false
        } else {
          this.selectExam = this.activeExam
          return true
        }
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    async init() {
      try {
        const user = await userProfileReq('get')
        if (user.data.course === null) {
          this.$Modal.info({ title: '请在课程信息/课程总览页面选择当前课程' })
        } else {
          const examActive = await this.loadExams()
          this.curCourse = user.data.course.id
          const room = await roomReq('get')
          this.classrooms = room.data['rooms']
            .filter((item) => {
              return item['available']
            })
            .sort((o1, o2) => {
              if (o1['name'] == o2['name']) {
                return 0
              } else {
                return o1['name'] < o2['name'] ? -1 : 1
              }
            })
          this.selectRoom(this.curRoomId === null ? this.classrooms[0].id : this.curRoomId)
          if (examActive) {
            this.loadQueue()
            this.queueTimer = setInterval(this.loadQueue, 10000)
          }
          this.loadEr()
        }
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    onTabUpdate(name) {
      this.$store.commit('onClass/setOverviewTab', name)
    },
    onRadioUpdate(label) {
      this.$store.commit('onClass/setOverviewRadio', label)
    },
    loadQueue() {
      if (this.curRoomId) {
        const params = { roomId: this.curRoomId, valid: true }
        queueReq(params)
          .then((res) => {
            this.queueTableData = res.data.queue
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
        queueReq({ roomId: this.curRoomId, valid: false })
          .then((res) => {
            this.takeOutData = res.data.queue
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
      }
    },
    loadEr() {
      if (this.curRoomId) {
        getRoomErReq(this.curRoomId, { exam__exact: this.selectExam })
          .then((res) => {
            this.roomER = res.data['exam-records'].map((item) => {
              let status = item.status === 2 && item.check_result !== -1 ? 4 : item.status
              return {
                ...item,
                status: status,
                status_name: recordStatus[status].name,
                status_color: recordStatus[status].color,
                checked_in_at: moment(item.checked_in_at).format('HH:mm:ss'),
                checked_out_at: moment(item.checked_out_at).format('HH:mm:ss')
              }
            })
          })
          .catch((error) => {
            this.$Modal.error(getErrModalOptions(error))
          })
      }
    },
    onUndoHandUp(student_id, project_in_exam) {
      undoHandsUp(this.curRoomId, student_id, project_in_exam)
        .then(() => {
          this.$Notice.success({ title: '撤销成功' })
          this.loadQueue()
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    onPushTestStudent() {
      const examId = parseInt(this.testHandUpExamInput)
      testHandUpReq(this.curRoomId, this.testHandUpStudentInput, { exam: isNaN(examId) ? null : examId })
        .then(() => {
          this.reloadCurRoom(this.curRoomId)
          this.$Notice.success({ title: '加入队列成功' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    checkCheckRecord(record) {
      if (record.status !== 0) {
        this.$router.push({ name: 'check_exam_record', params: { id: record.id } })
      } else {
        this.$Notice.info({ title: '该学生未签退，无成绩' })
      }
    },
    gotoCheckRecord(record) {
      this.$router.push({ name: 'check_exam_record', params: { id: record } })
    }
  }
}
</script>
