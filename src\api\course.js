import { getRequest, getRequestForAnnounce } from '@/api/util'

export const courseReq = (method, params) => {
  return getRequest(`/api/courses`, method, params)
}

export const courseIdReq = (method, id, params) => {
  return getRequest(`/api/courses/${id}`, method, params)
}

export const announcementReq = (method, params) => {
  return getRequestForAnnounce(`/api/announcement`, method, params)
}

export const addRetakeStudent = (courseId, params) => {
  return getRequest(`/api/courses/${courseId}/retake_student`, 'put', params)
}

export const getAllowedUserReq = (id, params) => {
  return getRequest(`/api/courses/${id}/allowed-users`, 'get', params)
}

export const assistantReq = (method, id, params) => {
  return getRequest(`/api/courses/${id}/assistants`, method, params)
}
