import { getRequest } from '@/api/util'

export const clearRoomReq = (method, examId, roomId) => {
  return getRequest(`/api/exam-clear-room`, method, { exam_id: examId, room_id: roomId })
}

export const queueReq = (params) => {
  return getRequest(`/api/exam-queue-list/${params.roomId}`, 'get', { valid: params.valid })
}

export const queuePopReq = (roomId, studentId) => {
  return getRequest(`/api/exam-queue/${roomId}`, 'put', { student_id: studentId })
}

export const checkInReq = (studentId) => {
  return getRequest(`/api/exam-check-in/${studentId}`, 'put')
}

export const handsUpReq = (studentId, pieId) => {
  return getRequest(`/api/exam-queue/${studentId}/${pieId}`, 'post')
}

export const roomArrangementReq = (id, method, params) => {
  return getRequest(`/api/room-arrangement/${id}`, method, params)
}

export const studentSeatReq = (studentId, method, params) => {
  return getRequest(`/api/student-seat/${studentId}`, method, params)
}

export const studentCurrentSeat = (studentId, examId) => {
  return getRequest(`/api/student-seat/${studentId}?exam_id=${examId}`, 'get')
}

export const checkOutReq = (method, studentId, params) => {
  return getRequest(`/api/exam-check-out/${studentId}`, method, params)
}

export const getRoomErReq = (roomId, params) => {
  return getRequest(`/api/room-examrecord/${roomId}`, 'get', params)
}

export const getQueueHead = (roomId) => {
  return getRequest(`/api/exam-queue/${roomId}`, 'get')
}

export const undoCheckOutReq = (studentId) => {
  return getRequest(`/api/roll-back-check-out/${studentId}`, 'put')
}

export const undoCheckInReq = (studentId) => {
  return getRequest(`/api/roll-back-check-in/${studentId}`, 'put')
}

export const undoHandsUp = (roomId, studentId, pie) => {
  return getRequest(`/api/exam-queue/${roomId}/${studentId}/${pie}/cancelHandsUp`, 'post')
}
