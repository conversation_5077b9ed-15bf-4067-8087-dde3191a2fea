@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot') format('embedded-opentype'), url('iconfont.woff') format('woff'),
    url('iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: 'iconfont', sans-serif !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-bear:before {
  content: '\e600';
}

.icon-resize-vertical:before {
  content: '\e7c3';
}

.icon-chuizhifanzhuan:before {
  content: '\e661';
}

.icon-shuipingfanzhuan:before {
  content: '\e662';
}

.icon-qq:before {
  content: '\e609';
}

.icon-frown:before {
  content: '\e77e';
}

.icon-meh:before {
  content: '\e780';
}

.icon-smile:before {
  content: '\e783';
}

.icon-man:before {
  content: '\e7e2';
}

.icon-woman:before {
  content: '\e7e5';
}
