import { getRequest, direct } from '@/api/util'

export const examReq = (method, params) => {
  return getRequest(`/api/exams`, method, params)
}

export const examIdReq = (method, examId, params) => {
  return getRequest(`/api/exams/${examId}`, method, params)
}

export const examQuestionReq = (method, examId, params) => {
  return getRequest(`/api/project-in-exam/${examId}/question`, method, params)
}

export const examSeatReq = (method, examId, params) => {
  return getRequest(`/api/seat_arrangements/${examId}`, method, params)
}

export const examProjectReq = (method, params) => {
  return getRequest(`/api/project-in-exam`, method, params)
}

export const examProjectListReq = (method, examId, params) => {
  return getRequest(`/api/project-in-exam/${examId}/exam`, method, params)
}

export const examProjectIdReq = (method, projectExamId, params) => {
  return getRequest(`/api/project-in-exam/${projectExamId}`, method, params)
}

export const examProjectRequireReq = (method, projectExamId, params) => {
  return getRequest(`/api/project-in-exam/${projectExamId}/requirement`, method, params)
}

export const examProjectDownloadErrorInfo = (method, projectExamId, params) => {
  return getRequest(`/api/project-in-exam/${projectExamId}/download-error-info`, method, params)
}

export const examProgressUnderclass = (courseId, method, params) => {
  return getRequest(`/api/progress/${courseId}/underclass`, method, params)
}

export const examProgressUnderclassForce = (courseId) => {
  return direct(`/api/progress/${courseId}/underclass`, 'post', { params: { force: 1 } })
}

export const examProgress = (courseId, method, params) => {
  return getRequest(`/api/progress/${courseId}`, method, params)
}

export const examProgressPushToInClass = (courseId, method, params) => {
  return getRequest(`/api/push-all-student-before-exam/${courseId}`, method, params)
}

export const examProgressPushToUnderClass = (courseId, method, params) => {
  return getRequest(`/api/push-all-student-after-exam/${courseId}`, method, params)
}

export const examProgressInit = (courseId, method, params) => {
  return getRequest(`/api/progress/${courseId}/init`, method, params)
}

export const getExamStudent = (examId) => {
  return getRequest(`/api/exams/${examId}/students`, 'get', {})
}

export const getProjectFailAnalysis = (projectExamId) => {
  return getRequest(`/api/fail-analysis/${projectExamId}`, 'get', {})
}

export const pieCSV = (pie) => {
  return getRequest(`/api/project-in-exam/${pie}/csv`, 'get')
}

export const checkExam = (examId) => {
  return getRequest(`/api/progress/statistics/${examId}`, 'get')
}

export const examProgressCSV = (courseId, params) => {
  return getRequest(`/api/progress/${courseId}/csv`, 'get', params)
}

export const progressSkip = (courseId, studentId) => {
  return getRequest(`/api/progress/${courseId}/skip/${studentId}`, 'put')
}

export const examProgressRetakeInit = (courseId) => {
  return getRequest(`/api/progress/${courseId}/retake-init`, 'post')
}

export const examProgressRetakeReset = (courseId, params) => {
  return getRequest(`/api/progress/${courseId}/reset-retake-progress`, 'post', params)
}

export const nonSubmitStudent = (courseId) => {
  return getRequest(`/api/non_submit_student?course_id=${courseId}`, 'get')
}

export const statisticsCombineReq = (params) => {
  return getRequest(`/api/progress/statistics-combine`, 'get', params)
}

export const statisticsCombineClass = (params) => {
  return getRequest(`/api/progress/statistics-combine-class`, 'get', params)
}

export const createNewsReq = (params) => {
  return getRequest(`/api/news`, 'post', params)
}

export const listNewsReq = (examId) => {
  return getRequest(`/api/news`, 'get', { exam_id__exact: examId })
}

export const deleteNewsReq = (newsId) => {
  return getRequest(`/api/news`, 'delete', { id: newsId })
}

export const classProgress = (courseId, retake, method, params) => {
  if (retake) {
    // 有重修生
    return getRequest(`/api/progress/${courseId}/class`, method, params)
  } else {
    // 无重修生
    return getRequest(`/api/progress/${courseId}/no-retake-class`, method, params)
  }
}

export const getStudentsExamRecord = (courseId, examId) => {
  return getRequest(`/api/progress/${courseId}/exam-student/${examId}`, 'get')
}

export const getConsecutiveFailsStudents = (courseId) => {
  return getRequest(`/api/students/consecutive-fails/${courseId}`, 'get')
}

export const getConsecutiveNoQualificationStudents = (courseId) => {
  return getRequest(`/api/students/consecutive-no-qualification/${courseId}`, 'get')
}
