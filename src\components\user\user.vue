<template>
  <div class="user-avatar-dropdown">
    <Dropdown @on-click="handleClick">
      <Avatar :src="require('@/assets/images/scse.png')" style="margin-bottom: 3px" />
      <Icon :size="18" type="md-arrow-dropdown" />
      <span slot="list">
        <DropdownMenu>
          <DropdownItem name="updatePassword">修改密码</DropdownItem>
          <DropdownItem name="updateCourse">修改课程</DropdownItem>
          <DropdownItem name="logout">退出登录</DropdownItem>
        </DropdownMenu>
      </span>
    </Dropdown>
  </div>
</template>

<script>
import './user.less'
import { mapActions } from 'vuex'
// import { Store } from 'vuex'

export default {
  name: 'User',
  methods: {
    ...mapActions({
      handleLogOut: 'user/handleLogOut'
    }),
    logout() {
      this.handleLogOut()
    },
    updatePassword() {
      this.$router.push({ name: 'update_password' })
    },
    updateCourse() {
      this.$router.push({ name: 'user_detail_self', params: {} })
    },
    handleClick(name) {
      switch (name) {
        case 'logout':
          this.logout()
          break
        case 'updatePassword':
          this.updatePassword()
          break
        case 'updateCourse':
          this.updateCourse()
          break
      }
    }
  }
}
</script>
