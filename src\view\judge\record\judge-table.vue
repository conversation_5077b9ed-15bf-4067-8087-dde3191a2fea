<template>
  <Card>
    <filter-table
      ref="filterTableRef"
      :data="tableData"
      :columns="columns"
      :default-filter="$store.state.app.tableFilter['judgeTable'] ? $store.state.app.tableFilter.judgeTable : {}"
      @on-search="onSearch"
    />
    <div style="margin: 10px; overflow: hidden">
      <div style="margin-right: 10px; float: left" offset="24">
        <Button type="primary" @click="refresh">刷新</Button>
      </div>
      <div style="float: left">
        <Button :type="hideUnimportantStyle" @click="hideUnimportant()">{{ hideUnimportantText }}</Button>
      </div>
      <div style="float: left; margin-left: 15px; margin-top: 4px">
        <i-switch :value="interval !== null" @input="handleRefresh" size="large">
          <span slot="open">轮询</span>
          <span slot="close">轮询</span>
        </i-switch>
      </div>
      <div style="float: right">
        <Page
          :total="totalCnt"
          :current="curPage"
          :page-size="pageSize"
          show-elevator
          show-total
          @on-change="changePage"
        >
          <p>总数{{ allRecords }}条，当前已筛选出来{{ totalCnt }}条</p>
        </Page>
      </div>
    </div>
  </Card>
</template>

<script>
import { judgeReq, judgeRecordFileReq } from '@/api/judge'
import FilterTable from '@/view/filter-table/filter-table'
import { getErrModalOptions, getLocalTime, processRemoteDownload } from '@/libs/util'
import _ from 'lodash'
import { Tag, Link, LinkDownload, WhitePre } from '@/libs/render-item'

const judgeStatus = {
  0: {
    value: 0,
    name: '通过',
    color: 'green'
  },
  1: {
    value: 1,
    name: '未通过',
    color: 'red'
  },
  2: {
    value: -1,
    name: '测评中',
    color: 'orange'
  },
  3: {
    value: -2,
    name: '排队中',
    color: 'yellow'
  },
  4: {
    value: 2,
    name: '错误',
    color: 'red'
  }
}

export default {
  name: 'JudgeTable',
  components: { FilterTable },
  data() {
    return {
      interval: null,
      tableData: [],
      origin: [],
      columns: [
        {
          title: 'ID',
          key: 'id',
          filter: {
            type: 'Input'
          },
          render: (h, params) => Link(h, params.row.id, 'judge_detail')
        },
        {
          title: 'Edx Username',
          key: 'edx_username',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row['edx_username'])
        },
        {
          title: 'Attachment',
          key: 'attachment__filename',
          filter: {
            type: 'Input'
          },
          render: (h, params) =>
            LinkDownload(
              h,
              () => this.onDownload(params.row.id),
              params.row['attachment__filename'],
              params.row['attachment__filename'] === null
            )
        },
        {
          title: 'Judge Status',
          key: 'judge_result',
          render: (h, params) => {
            const color =
              params.row.judge_result === 0
                ? 'green'
                : params.row.judge_result === -1
                ? 'orange'
                : params.row.judge_result === -2
                ? 'yellow'
                : params.row.judge_result === 2
                ? '#EF9A9A'
                : 'red'
            const text =
              params.row.judge_result === 0
                ? 'PASSED'
                : params.row.judge_result === -1
                ? 'JUDGING'
                : params.row.judge_result === -2
                ? 'QUEUEING'
                : params.row.judge_result === 2
                ? 'ERROR'
                : 'FAILED'
            return Tag(h, color, text)
          },
          filter: {
            type: 'Select',
            option: judgeStatus
          }
        },
        {
          title: 'PID',
          key: 'problem__id',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row['problem__id'])
        },
        {
          title: 'Tool Chain',
          key: 'tool_chain',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Problem Name',
          key: 'problem__name',
          filter: {
            type: 'Input'
          },
          render: (h, params) => WhitePre(h, params.row['problem__name'])
        },
        {
          title: 'Judger Identifier',
          key: 'judger_identifier',
          filter: {
            type: 'Input'
          }
        },
        {
          title: 'Submit At',
          key: 'submitted_at',
          render: (h, params) => h('div', getLocalTime(params.row.submitted_at))
        },
        {
          title: 'Start At',
          key: 'started_at',
          render: (h, params) => h('div', getLocalTime(params.row.started_at))
        },
        {
          title: 'Finish At',
          key: 'finished_at',
          render: (h, params) => h('div', getLocalTime(params.row.finished_at))
        }
      ],
      totalCnt: 0,
      pageSize: 10,
      curPage: 1,
      order_by: '-submitted_at',
      filter: {},
      allRecords: 0,
      showUnimportant: null,
      hideUnimportantStyle: null,
      hideUnimportantText: null
    }
  },
  created() {
    this.origin.push(...this.columns)
    this.showUnimportant = this.$store.getters['view/showUnimportant']
    if (this.showUnimportant === null) {
      this.showUnimportant = false
    }
    if (this.showUnimportant === false) {
      this.columns.splice(-4)
      this.columns.splice(-2, 1)
      this.hideUnimportantStyle = 'default'
      this.hideUnimportantText = '展示次要列'
    } else {
      this.hideUnimportantStyle = 'primary'
      this.hideUnimportantText = '隐藏次要列'
    }
  },
  mounted() {
    if (this.$store.state.app.tableFilter.judgeTable) {
      this.refactorSearchObject(this.$store.state.app.tableFilter.judgeTable)
    }
    this.curPage = this.$store.state.app.tablePage.judgeTable ? this.$store.state.app.tablePage.judgeTable : 1
    this.loadData(this.curPage)
  },
  beforeDestroy() {
    if (this.interval !== null) {
      clearInterval(this.interval)
      this.interval = null
    }
  },
  methods: {
    loadData(index) {
      judgeReq('get', {
        page: index,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data.models
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.allRecords = res.data['models_all']
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'judgeTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    changePage(index) {
      this.loadData(index)
    },
    async onDownload(judgeId) {
      try {
        const res = await judgeRecordFileReq('get', judgeId)
        processRemoteDownload(res)
      } catch (error) {
        this.$Modal.error(getErrModalOptions(error))
      }
    },
    onSearch(search) {
      search = this.refactorSearchObject(search)
      judgeReq('get', {
        page: 1,
        page_size: 10,
        order_by: this.order_by,
        ...this.filter
      })
        .then((res) => {
          this.tableData = res.data.models
          this.totalCnt = res.data['total_count']
          this.curPage = res.data['page_now']
          this.allRecords = res.data['models_all']
          this.$store.commit('setTableFilter', { filter: search, name: 'judgeTable' })
          this.$store.commit('setTablePage', { page: res.data['page_now'], name: 'judgeTable' })
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
        })
    },
    refactorSearchObject(search) {
      const searchNew = _.omitBy(search, (value) => {
        return typeof value !== 'string' || value === ''
      })
      this.filter = {}
      Object.keys(search).forEach((key) => {
        if (key === 'problem__id' || key === 'judge_result' || key === 'judger_identifier' || key === 'id') {
          this.filter[key + '__exact'] = search[key]
        } else {
          this.filter[key + '__contains'] = search[key]
        }
      })
      return searchNew
    },
    refresh() {
      this.loadData(1)
    },
    handleRefresh() {
      if (this.interval === null) {
        this.interval = setInterval(() => this.loadData(1), 8000)
      } else {
        clearInterval(this.interval)
        this.interval = null
      }
    },
    hideUnimportant() {
      this.showUnimportant = !this.showUnimportant
      this.$store.commit('view/updateShowUnimportant', this.showUnimportant)
      if (!this.showUnimportant) {
        this.columns.splice(-4)
        this.columns.splice(-2, 1)
        this.hideUnimportantStyle = 'default'
        this.hideUnimportantText = '展示次要列'
      } else {
        this.columns.splice(-1, 0, this.origin.slice(-6)[0])
        this.origin.slice(-4).map((item) => this.columns.push(item))
        this.hideUnimportantStyle = 'primary'
        this.hideUnimportantText = '隐藏次要列'
      } // 隐藏五个无关列
      this.$refs.filterTableRef.loadFilterHeader()
    }
  }
}
</script>
