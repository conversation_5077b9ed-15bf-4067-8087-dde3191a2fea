<template>
  <Row>
    <Col span="8" offset="6">
      <Card>
        <Form ref="examNew" :model="newExam" :rules="examRule" :label-width="110">
          <form-item prop="date" label="考试时间">
            <date-picker type="date" placeholder="选择日期" style="width: 200px" @on-change="onChange" />
          </form-item>
          <form-item>
            <Button type="primary" @click="handleSubmit('examNew')">确认创建</Button>
          </form-item>
        </Form>
      </Card>
    </Col>
  </Row>
</template>

<script>
import { examReq } from '@/api/exam'
import { getErrModalOptions } from '@/libs/util'

export default {
  name: 'ExamCreate',
  data() {
    return {
      newExam: {
        date: null,
        active: false
      },
      examRule: {
        date: [{ required: true, message: '请选择考试日期', trigger: 'blur' }]
      }
    }
  },
  methods: {
    upload(data) {
      return examReq('post', data)
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.upload(this.newExam)
            .then(() => {
              this.$Notice.success({ title: '创建成功' })
              this.$router.push({ name: 'exam_table' })
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        } else {
          this.$Notice.warning({ title: '表单验证失败' })
        }
      })
    },
    onChange(date) {
      this.newExam.date = date
    }
  }
}
</script>
