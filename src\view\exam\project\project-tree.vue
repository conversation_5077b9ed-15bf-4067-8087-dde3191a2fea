<template>
  <div style="height: 100%; width: 100%; overflow: hidden">
    <Button type="primary" @click="onCreate">添加新项目</Button>
    <Card shadow style="height: 95%; width: 100%; overflow: hidden">
      <div class="department-outer">
        <div class="zoom-box">
          <zoom-controller v-model="zoom" :min="20" :max="200" />
        </div>
        <div class="view-box">
          <tree-view
            v-if="treeData"
            :data="treeData"
            :zoom-handled="zoomHandled"
            @delete="onDelete"
            @update="onUpdate"
          />
          <div v-if="!treeData">empty</div>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import { projectReq, projectReqWithId } from '@/api/project'
import { getErrModalOptions } from '@/libs/util'
import _ from 'lodash'
import './project-tree.less'
import ZoomController from '@/view/exam/project/zoom-controller'
import { userProfileReq } from '@/api/user'
import TreeView from '@/view/exam/project/project-tree-view'

export default {
  name: 'Project',
  components: { TreeView, ZoomController },
  data() {
    return {
      projects: [],
      zoom: 100
    }
  },
  computed: {
    zoomHandled() {
      return this.zoom / 100
    },
    treeData() {
      const emptyData = { id: 0, label: '当前课程无 Project 信息' }
      const projects = _.map(this.projects, (item) => {
        item.label = item.name
        item.children = []
        return item
      })
      const roots = _.filter(projects, (item) => item['parent_project'] === null)
      if (roots.length === 0) {
        return emptyData
      }
      let root = roots[0]
      if (roots.length > 1) {
        // if root project is multiple
        // add a virtual root node and let all root project point to it
        const virtualRoot = { parent_project: null, id: '__v_root__', label: '项目总览', children: [] }
        _.forEach(projects, (item) => {
          if (item['parent_project'] === null) {
            item['parent_project'] = '__v_root__'
          }
        })
        projects.push(virtualRoot)
        root = virtualRoot
      }
      _.forEach(projects, (item) => {
        const parent = item['parent_project']
        if (parent === null) return
        _.forEach(projects, (another) => {
          if (another.id === parent) {
            another.children = [...another.children, item]
          }
        })
      })
      return root
    }
  },
  mounted() {
    this.getProjects()
  },
  methods: {
    async getProjects() {
      try {
        const userProfileBody = await userProfileReq('get')
        if (userProfileBody.data.course) {
          const course = userProfileBody.data.course.id
          let filter = { course__id__exact: course }
          let projectBody = await projectReq('get', filter)
          const total_count = projectBody.data['total_count']
          if (total_count > 10) {
            filter.page_size = total_count
            projectBody = await projectReq('get', filter)
          }
          this.projects = projectBody.data.models
        } else {
          this.$Modal.info({
            title: '未选择当前课程',
            content: '请在课程列表选择当前课程'
          })
        }
      } catch (e) {
        this.$Modal.error(getErrModalOptions(e))
      }
    },
    onUpdate(project) {
      this.$router.push({ name: 'project_detail', params: { id: project.id } })
    },
    onCreate() {
      this.$router.push({ name: 'project_detail' })
    },
    onDelete(project) {
      this.$Modal.confirm({
        title: '确认删除',
        onOk: () => {
          projectReqWithId('delete', project.id)
            .then(() => {
              this.$Notice.success({ title: '删除成功' })
              this.getProjects()
            })
            .catch((error) => {
              this.$Modal.error(getErrModalOptions(error))
            })
        }
      })
    }
  }
}
</script>
