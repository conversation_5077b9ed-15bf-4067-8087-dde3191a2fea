<template>
  <Card>
    <Table v-show="false" ref="table" />
    <Card>
      <div style="display: inline-block; vertical-align: middle; margin-left: 10px; margin-bottom: 10px">
        显示重修生：
      </div>
      <i-switch v-model="switch_retaker" size="large" @on-change="loadCurCourse" style="margin-bottom: 10px">
        <span slot="open">开启</span>
        <span slot="close">关闭</span>
      </i-switch>
      <class-chart
        :teacher="teachers"
        :series="series"
        :totalData="totalData"
        style="height: 400px"
        :text="'当前各班实验进度（最终完成课上进度+1）'"
      />
    </Card>
    <br />
    <Card>
      <Row type="flex" justify="center">
        <Select v-model="selectWeek" placeholder="请选择周次" style="width: 300px; margin-bottom: 18px">
          <Option v-for="exam in examList" :key="exam.id" :value="exam.id">{{ exam.id }} : {{ exam.date }}</Option>
        </Select>
        <Button type="primary" :disabled="!selectWeek" style="margin-left: 10px" @click="downloadCSV">
          导出 CSV
        </Button>
      </Row>
      <Row>
        <class-chart
          :teacher="teachers"
          :series="seriesWeekPasses"
          :totalData="totalDataWeekPassed"
          style="height: 400px"
          :text="'单周各班课上通过情况'"
        />
        <class-chart
          :teacher="teachers"
          :series="seriesWeekFailes"
          :totalData="totalDataWeekFailed"
          style="height: 400px"
          :text="'单周各班课上未通过情况'"
        />
      </Row>
    </Card>
  </Card>
</template>

<script>
import { userProfileReq } from '@/api/user'
import { classProgress, examReq, statisticsCombineClass, getStudentsExamRecord } from '@/api/exam'
import { courseIdReq } from '@/api/course'
import { getErrModalOptions } from '@/libs/util'
import ClassChart from './class-chart.vue'

export default {
  name: 'ClassChartIndex',
  components: { ClassChart },
  data() {
    return {
      curCourse: null,
      PList: [],
      totalData: [],
      examList: [],
      selectWeek: null,
      series: [],
      totalDataWeekPassed: [],
      totalDataWeekFailed: [],
      seriesWeekPasses: null,
      seriesWeekFailes: null,
      teachers: [],
      switch_retaker: true,
      allData: null,
      chartColorsNormal: ['#E7F3FF', '#B2D2FF', '#92A9D0', '#7A8DA5', '#5A4A61', '#3B2A3C'],
      chartColorsPassed: ['#A4C8E1', '#82B4E0', '#5FA1E3', '#2384D0', '#126FAE', '#0A4A7C'],
      chartColorsFailed: [
        '#F4E2C4',
        '#EBD5B1',
        '#E3C19D',
        '#D6A86D',
        '#C68A4D',
        '#B5723C',
        '#A55B2E',
        '#933F1F',
        '#7A2610'
      ]
    }
  },
  computed: {},
  mounted() {
    this.loadCurCourse()
  },
  watch: {
    selectWeek(curExamId) {
      this.loadWeekData(curExamId)
    }
  },
  methods: {
    loadCurCourse() {
      var underClassId
      userProfileReq('get')
        .then((res) => {
          if (res.data.course === null) {
            this.$Modal.info({
              title: '请在用户详情页选择当前课程'
            })
          } else {
            this.curCourse = res.data.course.id
            this.loadClass()
            return courseIdReq('get', this.curCourse, {})
          }
        })
        .then((res) => {
          underClassId = res.data.under_class_exam
          return examReq('get', { page_size: 1000, course_id: this.curCourse })
        })
        .then((res) => {
          this.examList = res.data.exams.filter((item) => item.id !== underClassId)
          return statisticsCombineClass({
            exam_id_list: '[' + this.examList.map((item) => item.id) + ']',
            course_id: this.curCourse
          })
        })
        .then((res) => {
          this.allData = res.data
          for (const exam in this.allData.exam_statistics) {
            for (const p in this.allData.exam_statistics[exam]) {
              if (!this.PList.includes(p)) {
                this.PList.push(p)
              }
            }
          }
        })
        .catch((error) => {
          this.$Modal.error(getErrModalOptions(error))
          return
        })
    },
    loadClass() {
      this.teachers = []
      this.totalData = []
      this.series = []
      classProgress(this.curCourse, this.switch_retaker, 'get', {}).then((res) => {
        // 获取所有班级的所有P值并去重
        this.PList = [...new Set(Object.values(res.data.class).flatMap((classInfo) => Object.keys(classInfo)))].sort()

        Object.entries(res.data.class).forEach(([teacherName]) => {
          this.teachers.push(teacherName)
        })
        let pData = {}
        this.PList.forEach((item) => {
          pData[item] = []
        })
        Object.entries(res.data.class).forEach(([, classInfo]) => {
          let total = 0
          this.PList.forEach((pName) => {
            let cnt = classInfo[pName]
            if (cnt === undefined) {
              cnt = 0
            }
            total = total + cnt
          })
          if (total == 0) {
            total = 1
          }
          this.totalData.push(total)

          this.PList.forEach((pName) => {
            let cnt = classInfo[pName]
            if (cnt === undefined) {
              cnt = 0
            }
            pData[pName].push((cnt / total) * 100.0)
          })
        })
        this.series = this.generateChartSeries(pData, this.chartColorsNormal, '')
      })
    },
    // 加载某个考试的信息用于计算某周的通过情况
    loadWeekData(examId) {
      let pPersonPassed = {}
      let pPersonFailed = {}
      let pDataPassed = {}
      let pDataFailed = {}
      let totalCntPassed = {}
      let totalCntFailed = {}

      const examData = this.allData.exam_statistics[examId]

      // 移除之前的数据
      this.totalDataWeekPassed.length = 0
      this.totalDataWeekFailed.length = 0
      // 对于所有老师（班级）的列表循环
      this.teachers.forEach((teacherName) => {
        totalCntPassed[teacherName] = 0
        totalCntFailed[teacherName] = 0
        this.PList.forEach((pName) => {
          try {
            // 对该P，该班级通过考试的学生的总数
            totalCntPassed[teacherName] += examData[pName][teacherName].normal_student.passed.count
            totalCntFailed[teacherName] += examData[pName][teacherName].normal_student.failed.count
          } catch (e) {
            // 有异常说明是用到了undefined，就当是0处理了
          }
        })
        this.totalDataWeekPassed.push(totalCntPassed[teacherName])
        this.totalDataWeekFailed.push(totalCntFailed[teacherName])
      })

      // 初始化每个P各个老师老师在某个P的学生总人数
      this.PList.forEach((pName) => {
        pPersonPassed[pName] = {}
        pPersonFailed[pName] = {}
        this.teachers.forEach((teacherName) => {
          pPersonPassed[pName][teacherName] = 0
          pPersonFailed[pName][teacherName] = 0
        })
      })

      // 统计每个老师在某个P的学生总人数
      this.PList.forEach((pName) => {
        this.teachers.forEach((teacherName) => {
          try {
            pPersonPassed[pName][teacherName] += examData[pName][teacherName].normal_student.passed.count
            pPersonFailed[pName][teacherName] += examData[pName][teacherName].normal_student.failed.count
          } catch (e) {
            // 有异常说明是用到了undefined，就当是0处理了
          }
        })
      })

      // 计算占比数据
      this.PList.forEach((pName) => {
        pDataPassed[pName] = []
        pDataFailed[pName] = []
        this.teachers.forEach((teacherName) => {
          try {
            pDataPassed[pName].push((pPersonPassed[pName][teacherName] / totalCntPassed[teacherName]) * 100)
            pDataFailed[pName].push((pPersonFailed[pName][teacherName] / totalCntFailed[teacherName]) * 100)
          } catch (e) {
            pDataPassed[pName].push(0.0)
            pDataFailed[pName].push(0.0)
          }
        })
      })
      // 生成图表的series
      this.seriesWeekPasses = this.generateChartSeries(pDataPassed, this.chartColorsPassed, '')
      this.seriesWeekFailes = this.generateChartSeries(pDataFailed, this.chartColorsFailed, '_failed')
    },

    // 新增方法：生成图表序列数据
    generateChartSeries(pData, chartColors, append_text) {
      let seriesWeek = []
      Object.entries(pData).forEach(([pName, list], index) => {
        seriesWeek.push({
          name: pName + append_text,
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          data: list,
          itemStyle: {
            color: chartColors[index % chartColors.length]
          }
        })
      })
      return seriesWeek
    },

    downloadCSV() {
      if (!this.selectWeek) {
        this.$Message.warning('请先选择周次')
        return
      }

      getStudentsExamRecord(this.curCourse, this.selectWeek)
        .then((res) => {
          const csvData = res.data.data.split('\n')
          csvData.shift() // delete header
          csvData.pop() // delete empty line

          this.$refs.table.exportCsv({
            filename: res.data.filename,
            columns: [
              { key: 'student_id', title: '学号' },
              { key: 'student_name', title: '姓名' },
              { key: 'project_name', title: '项目名称' },
              { key: 'class_name', title: '教学班' },
              { key: 'status', title: '状态' },
              { key: 'retake', title: '是否重修' }
            ],
            data: csvData.map((item) => {
              const [, student_id, student_name, project_name, class_name, status, retake] = item.split(',')
              return {
                student_id,
                student_name,
                project_name,
                class_name,
                status: status.replace(/\r/g, ''),
                retake: retake.replace(/\r/g, '')
              }
            })
          })
        })
        .catch((err) => {
          this.$Modal.error(getErrModalOptions(err))
        })
    }
  }
}
</script>
