@echo off
setlocal enabledelayedexpansion

REM synchronize system time
net start w32time
w32tm /config /update /manualpeerlist:cscore.buaa.edu.cn /syncfromflags:manual /reliable:yes
timeout /T 3
w32tm /resync /force

echo ====## [DANGER ZONE] wiping volume D: ##====
timeout /T 5
REM quick-format(clear) D:
format /X /Q /Y /V:D D:

REM replace old vm configuration
set "vm_conf=%USERPROFILE%\CO\co-eda-vm.vmx"
set "vm_conf_tmp=%TEMP%\covm_tmp.vmx"
del /Q "%vm_conf_tmp%" 2>nul
REM set "line_number=0"
for /f "delims=" %%a in (%vm_conf%) do (
    REM set /a "line_number+=1"
    set "line=%%a"
    echo !line! | findstr /C:"uuid.bios" >nul
    if !errorlevel!==0 (
		REM echo deleted %line_number%: !line!
    ) else (
        echo !line! | findstr /C:"uuid.location" >nul
        if !errorlevel!==0 (
			REM echo deleted %line_number%: !line!
        ) else (
            echo !line!>>"%vm_conf_tmp%"
        )
    )
)
move /Y "%vm_conf_tmp%" "%vm_conf%"

REM start vm
timeout /T 3
start "%PROGRAMFILES(X86)%\VMware\VMware Player\vmplayer.exe" "%vm_conf%"
