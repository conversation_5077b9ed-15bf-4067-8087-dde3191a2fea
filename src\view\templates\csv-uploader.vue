<template>
  <div>
    <Card>
      <Row>
        <Col span="22">
          <Tooltip placement="right">
            <Upload :before-upload="beforeUpload" action="">
              <Button icon="ios-cloud-upload-outline">上传 CSV 文件</Button>
            </Upload>
            <div slot="content" style="white-space: normal; word-break: keep-all">
              <p>CSV 文件列应包含</p>
              <p>{{ expectedColumnNames }}</p>
            </div>
          </Tooltip>
          <strong>
            <span style="font-size: small">请确保 CSV 文件的编码格式为 UTF-8</span>
          </strong>
        </Col>
        <Col span="2">
          <Button :disabled="tableData.length === 0" type="primary" long @click="upload">
            {{ uploadBtnMsg }}
          </Button>
        </Col>
      </Row>
      <Row>
        <Col>
          <Table :height="500" :columns="columns" :data="tableData" />
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'
import _ from 'lodash'

export default {
  name: 'CsvUploader',
  props: {
    message: {
      type: String,
      default: ''
    },
    expectedColumnNames: {
      type: Array,
      require: true,
      default: () => []
    },
    aliasColumnNames: {
      type: Object,
      default: () => ({})
    },
    handleUpload: {
      type: Function,
      require: true,
      default(data) {
        this.$Notice.warning({ title: 'handleUpload 未定义' })
        return new Promise((resolve, reject) => reject(data))
      }
    },
    handleCheck: {
      type: Function,
      require: false,
      default: (data) => new Promise((resolve) => resolve(data))
    }
  },
  data() {
    return {
      uploadedTableColumns: [],
      tableData: [],
      uploadBtnMsg: '确认上传'
    }
  },
  computed: {
    columns() {
      const indexCol = [
        {
          title: '序号',
          type: 'index',
          width: 100,
          align: 'center'
        }
      ]
      return indexCol.concat(
        this.expectedColumnNames.map((col) => ({
          title: this.aliasColumnNames[col] || col,
          key: col
        }))
      )
    },
    readyToUpload() {
      const columnNames = _.sortBy(
        this.uploadedTableColumns.map((col) => col.title),
        (item) => item
      )
      if (!columnNames || columnNames.length !== this.expectedColumnNames.length) {
        return false
      }
      const sorted = _.sortBy(this.expectedColumnNames, (item) => item)
      return columnNames.every((item, index) => item === sorted[index])
    }
  },
  methods: {
    resetData() {
      this.tableData = []
    },
    genderShow(data) {
      for (let index = 0; index < data.length; index++) {
        if (data[index].gender === 1) {
          data[index].gender = '男'
        } else if (data[index].gender === 2) {
          data[index].gender = '女'
        } else if (data[index].gender === 0) {
          data[index].gender = '未知'
        }
      }
      return data
    },
    beforeUpload(file) {
      getArrayFromFile(file)
        .then((data) => {
          let { columns, tableData } = getTableDataFromArray(data)
          tableData = this.genderShow(tableData)
          this.uploadedTableColumns = columns.filter((col) => this.expectedColumnNames.includes(col.title))
          const allowedColumns = this.expectedColumnNames
          this.tableData = tableData.map((item) => _.pick(item, allowedColumns))
          if (!this.readyToUpload) {
            this.uploadBtnMsg = '格式不符'
            this.$Notice.warning({ title: '格式不符' })
          } else {
            this.uploadBtnMsg = '确认上传'
          }
        })
        .catch((error) => {
          getErrModalOptions(error)
          this.$Notice.warning({ title: '只能上传 CSV 文件' })
        })
      return false
    },
    async upload() {
      try {
        await this.handleCheck(this.tableData)
        await this.handleUpload(this.tableData).then((res) => {
          this.$Modal.info({
            title: '上传成功',
            content:
              `成功上传${res.data['create_number']}条记录` +
              '<br>' +
              `忽略上传${res.data['duplicate_list'].length}条重复记录:` +
              '<br>' +
              `${res.data['duplicate_list']}`
          })
          this.resetData()
        })
      } catch (err) {
        this.$Modal.error(getErrModalOptions(err))
      }
    }
  }
}
</script>

<style lang="less" scoped>
.update-table-intro {
  margin-top: 10px;
}

.code-high-line {
  color: #2d8cf0;
}
</style>
